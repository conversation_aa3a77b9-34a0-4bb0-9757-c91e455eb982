# File Manually added to support streaming
# File is in libs instead of models to avoid conflicts with stainless bot

from typing import List, Optional

from .._models import BaseModel

__all__ = [
    "ChatCompletionChunk",
    "Choice",
    "ChoiceLogprobs",
    "ChoiceLogprobsContent",
    "ChoiceLogprobsContentTopLogprob",
    "ChoiceDelta",
    "ChoiceDeltaFunctionCall",
    "ChoiceDeltaToolCall",
    "ChoiceDeltaToolCallFunction",
]


class ChoiceDeltaFunctionCall(BaseModel):
    arguments: Optional[str] = None
    """
    The arguments to call the function with, as generated by the model in JSON
    format. Note that the model does not always generate valid JSON, and may
    hallucinate parameters not defined by your function schema. Validate the
    arguments in your code before calling your function.
    """

    name: Optional[str] = None
    """The name of the function to call."""


class ChoiceLogprobsContentTopLogprob(BaseModel):
    token: Optional[str] = None

    bytes: Optional[List[int]] = None

    logprob: Optional[float] = None


class ChoiceLogprobsContent(BaseModel):
    token: Optional[str] = None

    bytes: Optional[List[int]] = None

    logprob: Optional[float] = None

    top_logprobs: Optional[List[ChoiceLogprobsContentTopLogprob]] = None


class ChoiceLogprobs(BaseModel):
    content: Optional[List[ChoiceLogprobsContent]] = None


class ChoiceDeltaToolCallFunction(BaseModel):
    arguments: Optional[str] = None

    name: Optional[str] = None


class ChoiceDeltaToolCall(BaseModel):
    index: int

    id: Optional[str] = None

    function: Optional[ChoiceDeltaToolCallFunction] = None

    type: Optional[str] = None


class ChoiceDelta(BaseModel):
    content: str

    role: str

    function_call: Optional[ChoiceDeltaFunctionCall] = None


    tool_calls: Optional[List[ChoiceDeltaToolCall]] = None


class Choice(BaseModel):
    delta: ChoiceDelta

    finish_reason: str

    index: int

    logprobs: ChoiceLogprobs


class ChatCompletionChunk(BaseModel):
    id: str

    choices: List[Choice]

    created: int

    model: str

    object: str

    system_fingerprint: str

{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://config.googleapis.com/", "batchPath": "batch", "canonicalName": "Config", "description": "Creates and manages Google Cloud Platform resources and infrastructure.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/infrastructure-manager/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "config:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://config.mtls.googleapis.com/", "name": "config", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"get": {"description": "Gets information about a location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}", "httpMethod": "GET", "id": "config.projects.locations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Resource name for the location.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Location"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "config.projects.locations.list", "parameterOrder": ["name"], "parameters": {"extraLocationTypes": {"description": "Optional. A list of extra location types that should be used as conditions for controlling the visibility of the locations.", "location": "query", "repeated": true, "type": "string"}, "filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"deployments": {"methods": {"create": {"description": "Creates a Deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments", "httpMethod": "POST", "id": "config.projects.locations.deployments.create", "parameterOrder": ["parent"], "parameters": {"deploymentId": {"description": "Required. The Deployment ID.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent in whose context the Deployment is created. The parent value is in the format: 'projects/{project_id}/locations/{location}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/deployments", "request": {"$ref": "Deployment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}", "httpMethod": "DELETE", "id": "config.projects.locations.deployments.delete", "parameterOrder": ["name"], "parameters": {"deletePolicy": {"description": "Optional. Policy on how resources actuated by the deployment should be deleted. If unspecified, the default behavior is to delete the underlying resources.", "enum": ["DELETE_POLICY_UNSPECIFIED", "DELETE", "ABANDON"], "enumDescriptions": ["Unspecified policy, resources will be deleted.", "Deletes resources actuated by the deployment.", "Abandons resources and only deletes the deployment and its metadata."], "location": "query", "type": "string"}, "force": {"description": "Optional. If set to true, any revisions for this deployment will also be deleted. (Otherwise, the request will only work if the deployment has no revisions.)", "location": "query", "type": "boolean"}, "name": {"description": "Required. The name of the Deployment in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "deleteState": {"description": "Deletes Terraform state file in a given deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:deleteState", "httpMethod": "POST", "id": "config.projects.locations.deployments.deleteState", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:deleteState", "request": {"$ref": "DeleteStatefileRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportLock": {"description": "Exports the lock info on a locked deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:exportLock", "httpMethod": "GET", "id": "config.projects.locations.deployments.exportLock", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:exportLock", "response": {"$ref": "LockInfo"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "exportState": {"description": "Exports Terraform state file from a given deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:exportState", "httpMethod": "POST", "id": "config.projects.locations.deployments.exportState", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:exportState", "request": {"$ref": "ExportDeploymentStatefileRequest"}, "response": {"$ref": "Statefile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details about a Deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}", "httpMethod": "GET", "id": "config.projects.locations.deployments.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment. Format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Deployment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getIamPolicy": {"description": "Gets the access control policy for a resource. Returns an empty policy if the resource exists and does not have a policy set.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:getIamPolicy", "httpMethod": "GET", "id": "config.projects.locations.deployments.getIamPolicy", "parameterOrder": ["resource"], "parameters": {"options.requestedPolicyVersion": {"description": "Optional. The maximum policy version that will be used to format the policy. Valid values are 0, 1, and 3. Requests specifying an invalid value will be rejected. Requests for policies with any conditional role bindings must specify version 3. Policies with no conditional role bindings may specify any valid value or leave the field unset. The policy in the response might use the policy version that you specified, or it might use a lower policy version. For example, if you specify version 3, but the policy has no conditional role bindings, the response uses version 1. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "location": "query", "type": "integer"}, "resource": {"description": "REQUIRED: The resource for which the policy is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:getIamPolicy", "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "importState": {"description": "Imports Terraform state file in a given deployment. The state file does not take effect until the Deployment has been unlocked.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:importState", "httpMethod": "POST", "id": "config.projects.locations.deployments.importState", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:importState", "request": {"$ref": "ImportStatefileRequest"}, "response": {"$ref": "Statefile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Deployments in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments", "httpMethod": "GET", "id": "config.projects.locations.deployments.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Lists the Deployments that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field} {operator} {value}' where operators: '<', '>', '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = \"projects/foo/locations/us-central1/deployments/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Deployments in CREATING state. state=CREATING", "location": "query", "type": "string"}, "orderBy": {"description": "Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned by previous call to 'ListDeployments' which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent in whose context the Deployments are listed. The parent value is in the format: 'projects/{project_id}/locations/{location}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/deployments", "response": {"$ref": "ListDeploymentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "lock": {"description": "Locks a deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:lock", "httpMethod": "POST", "id": "config.projects.locations.deployments.lock", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:lock", "request": {"$ref": "LockDeploymentRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}", "httpMethod": "PATCH", "id": "config.projects.locations.deployments.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. Resource name of the deployment. Format: `projects/{project}/locations/{location}/deployments/{deployment}`", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}, "updateMask": {"description": "Optional. Field mask used to specify the fields to be overwritten in the Deployment resource by the update. The fields specified in the update_mask are relative to the resource, not the full request. A field will be overwritten if it is in the mask. If the user does not provide a mask then all fields will be overwritten.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "Deployment"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "setIamPolicy": {"description": "Sets the access control policy on the specified resource. Replaces any existing policy. Can return `NOT_FOUND`, `INVALID_ARGUMENT`, and `PERMISSION_DENIED` errors.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:setIamPolicy", "httpMethod": "POST", "id": "config.projects.locations.deployments.setIamPolicy", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy is being specified. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:setIamPolicy", "request": {"$ref": "SetIamPolicyRequest"}, "response": {"$ref": "Policy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "testIamPermissions": {"description": "Returns permissions that a caller has on the specified resource. If the resource does not exist, this will return an empty set of permissions, not a `NOT_FOUND` error. Note: This operation is designed to be used for building permission-aware UIs and command-line tools, not for authorization checking. This operation may \"fail open\" without warning.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:testIamPermissions", "httpMethod": "POST", "id": "config.projects.locations.deployments.testIamPermissions", "parameterOrder": ["resource"], "parameters": {"resource": {"description": "REQUIRED: The resource for which the policy detail is being requested. See [Resource names](https://cloud.google.com/apis/design/resource_names) for the appropriate value for this field.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+resource}:testIamPermissions", "request": {"$ref": "TestIamPermissionsRequest"}, "response": {"$ref": "TestIamPermissionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "unlock": {"description": "Unlocks a locked deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}:unlock", "httpMethod": "POST", "id": "config.projects.locations.deployments.unlock", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the deployment in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:unlock", "request": {"$ref": "UnlockDeploymentRequest"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"revisions": {"methods": {"exportState": {"description": "Exports Terraform state file from a given revision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}/revisions/{revisionsId}:exportState", "httpMethod": "POST", "id": "config.projects.locations.deployments.revisions.exportState", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent in whose context the statefile is listed. The parent value is in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:exportState", "request": {"$ref": "ExportRevisionStatefileRequest"}, "response": {"$ref": "Statefile"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details about a Revision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}/revisions/{revisionsId}", "httpMethod": "GET", "id": "config.projects.locations.deployments.revisions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Revision in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Revision"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Revisions of a deployment.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}/revisions", "httpMethod": "GET", "id": "config.projects.locations.deployments.revisions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Lists the Revisions that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field} {operator} {value}' where operators: '<', '>', '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = \"projects/foo/locations/us-central1/deployments/dep/revisions/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Revisions in CREATING state. state=CREATING", "location": "query", "type": "string"}, "orderBy": {"description": "Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "When requesting a page of resources, `page_size` specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned by previous call to 'ListRevisions' which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent in whose context the Revisions are listed. The parent value is in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/revisions", "response": {"$ref": "ListRevisionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"resources": {"methods": {"get": {"description": "Gets details about a Resource deployed by Infra Manager.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}/revisions/{revisionsId}/resources/{resourcesId}", "httpMethod": "GET", "id": "config.projects.locations.deployments.revisions.resources.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Resource in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}/resource/{resource}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+/revisions/[^/]+/resources/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Resource"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Resources in a given revision.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/deployments/{deploymentsId}/revisions/{revisionsId}/resources", "httpMethod": "GET", "id": "config.projects.locations.deployments.revisions.resources.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Lists the Resources that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field} {operator} {value}' where operators: '<', '>', '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = \"projects/foo/locations/us-central1/deployments/dep/revisions/bar/resources/baz", "location": "query", "type": "string"}, "orderBy": {"description": "Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "<PERSON><PERSON> returned by previous call to 'ListResources' which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent in whose context the Resources are listed. The parent value is in the format: 'projects/{project_id}/locations/{location}/deployments/{deployment}/revisions/{revision}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/deployments/[^/]+/revisions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/resources", "response": {"$ref": "ListResourcesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}, "operations": {"methods": {"cancel": {"description": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}:cancel", "httpMethod": "POST", "id": "config.projects.locations.operations.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be cancelled.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelOperationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "DELETE", "id": "config.projects.locations.operations.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource to be deleted.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "config.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "config.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "previews": {"methods": {"create": {"description": "Creates a Preview.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/previews", "httpMethod": "POST", "id": "config.projects.locations.previews.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent in whose context the Preview is created. The parent value is in the format: 'projects/{project_id}/locations/{location}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "previewId": {"description": "Optional. The preview ID.", "location": "query", "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes since the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+parent}/previews", "request": {"$ref": "Preview"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Preview.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/previews/{previewsId}", "httpMethod": "DELETE", "id": "config.projects.locations.previews.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the Preview in the format: 'projects/{project_id}/locations/{location}/previews/{preview}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/previews/[^/]+$", "required": true, "type": "string"}, "requestId": {"description": "Optional. An optional request ID to identify requests. Specify a unique request ID so that if you must retry your request, the server will know to ignore the request if it has already been completed. The server will guarantee that for at least 60 minutes after the first request. For example, consider a situation where you make an initial request and the request times out. If you make the request again with the same request ID, the server can check if original operation with the same request ID was received, and if so, will ignore the second request. This prevents clients from accidentally creating duplicate commitments. The request ID must be a valid UUID with the exception that zero UUID is not supported (00000000-0000-0000-0000-000000000000).", "location": "query", "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "export": {"description": "Export Preview results.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/previews/{previewsId}:export", "httpMethod": "POST", "id": "config.projects.locations.previews.export", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The preview whose results should be exported. The preview value is in the format: 'projects/{project_id}/locations/{location}/previews/{preview}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/previews/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:export", "request": {"$ref": "ExportPreviewResultRequest"}, "response": {"$ref": "ExportPreviewResultResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets details about a Preview.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/previews/{previewsId}", "httpMethod": "GET", "id": "config.projects.locations.previews.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the preview. Format: 'projects/{project_id}/locations/{location}/previews/{preview}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/previews/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "Preview"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Previews in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/previews", "httpMethod": "GET", "id": "config.projects.locations.previews.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Lists the Deployments that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field} {operator} {value}' where operators: '<', '>', '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case. Examples: - Filter by name: name = \"projects/foo/locations/us-central1/deployments/bar - Filter by labels: - Resources that have a key called 'foo' labels.foo:* - Resources that have a key called 'foo' whose value is 'bar' labels.foo = bar - Filter by state: - Deployments in CREATING state. state=CREATING", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. When requesting a page of resources, 'page_size' specifies number of resources to return. If unspecified, at most 500 will be returned. The maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token returned by previous call to 'ListDeployments' which specifies the position in the list from where to continue listing the resources.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent in whose context the Previews are listed. The parent value is in the format: 'projects/{project_id}/locations/{location}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/previews", "response": {"$ref": "ListPreviewsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "terraformVersions": {"methods": {"get": {"description": "Gets details about a TerraformVersion.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/terraformVersions/{terraformVersionsId}", "httpMethod": "GET", "id": "config.projects.locations.terraformVersions.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the TerraformVersion. Format: 'projects/{project_id}/locations/{location}/terraformVersions/{terraform_version}'", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/terraformVersions/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "TerraformVersion"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists TerraformVersions in a given project and location.", "flatPath": "v1/projects/{projectsId}/locations/{locationsId}/terraformVersions", "httpMethod": "GET", "id": "config.projects.locations.terraformVersions.list", "parameterOrder": ["parent"], "parameters": {"filter": {"description": "Optional. Lists the TerraformVersions that match the filter expression. A filter expression filters the resources listed in the response. The expression must be of the form '{field} {operator} {value}' where operators: '<', '>', '<=', '>=', '!=', '=', ':' are supported (colon ':' represents a HAS operator which is roughly synonymous with equality). {field} can refer to a proto or JSON field, or a synthetic field. Field names can be camelCase or snake_case.", "location": "query", "type": "string"}, "orderBy": {"description": "Optional. Field to use to sort the list.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. When requesting a page of terraform versions, 'page_size' specifies number of terraform versions to return. If unspecified, at most 500 will be returned. The maximum value is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. Token returned by previous call to 'ListTerraformVersions' which specifies the position in the list from where to continue listing the terraform versions.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent in whose context the TerraformVersions are listed. The parent value is in the format: 'projects/{project_id}/locations/{location}'.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/terraformVersions", "response": {"$ref": "ListTerraformVersionsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20250528", "rootUrl": "https://config.googleapis.com/", "schemas": {"ApplyResults": {"description": "Outputs and artifacts from applying a deployment.", "id": "ApplyResults", "properties": {"artifacts": {"description": "Location of artifacts (e.g. logs) in Google Cloud Storage. Format: `gs://{bucket}/{object}`", "type": "string"}, "content": {"description": "Location of a blueprint copy and other manifests in Google Cloud Storage. Format: `gs://{bucket}/{object}`", "type": "string"}, "outputs": {"additionalProperties": {"$ref": "TerraformOutput"}, "description": "Map of output name to output info.", "type": "object"}}, "type": "object"}, "AuditConfig": {"description": "Specifies the audit configuration for a service. The configuration determines which permission types are logged, and what identities, if any, are exempted from logging. An AuditConfig must have one or more AuditLogConfigs. If there are AuditConfigs for both `allServices` and a specific service, the union of the two AuditConfigs is used for that service: the log_types specified in each AuditConfig are enabled, and the exempted_members in each AuditLogConfig are exempted. Example Policy with multiple AuditConfigs: { \"audit_configs\": [ { \"service\": \"allServices\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" }, { \"log_type\": \"ADMIN_READ\" } ] }, { \"service\": \"sampleservice.googleapis.com\", \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\" }, { \"log_type\": \"DATA_WRITE\", \"exempted_members\": [ \"user:<EMAIL>\" ] } ] } ] } For sampleservice, this policy enables DATA_READ, DATA_WRITE and ADMIN_READ logging. It also exempts `<EMAIL>` from DATA_READ logging, and `<EMAIL>` from DATA_WRITE logging.", "id": "AuditConfig", "properties": {"auditLogConfigs": {"description": "The configuration for logging of each type of permission.", "items": {"$ref": "AuditLogConfig"}, "type": "array"}, "service": {"description": "Specifies a service that will be enabled for audit logging. For example, `storage.googleapis.com`, `cloudsql.googleapis.com`. `allServices` is a special value that covers all services.", "type": "string"}}, "type": "object"}, "AuditLogConfig": {"description": "Provides the configuration for logging a type of permissions. Example: { \"audit_log_configs\": [ { \"log_type\": \"DATA_READ\", \"exempted_members\": [ \"user:<EMAIL>\" ] }, { \"log_type\": \"DATA_WRITE\" } ] } This enables 'DATA_READ' and 'DATA_WRITE' logging, <NAME_EMAIL> from DATA_READ logging.", "id": "AuditLogConfig", "properties": {"exemptedMembers": {"description": "Specifies the identities that do not cause logging for this type of permission. Follows the same format of Binding.members.", "items": {"type": "string"}, "type": "array"}, "logType": {"description": "The log type that this config enables.", "enum": ["LOG_TYPE_UNSPECIFIED", "ADMIN_READ", "DATA_WRITE", "DATA_READ"], "enumDescriptions": ["Default case. Should never be this.", "Admin reads. Example: CloudIAM getIamPolicy", "Data writes. Example: CloudSQL Users create", "Data reads. Example: CloudSQL Users list"], "type": "string"}}, "type": "object"}, "Binding": {"description": "Associates `members`, or principals, with a `role`.", "id": "Binding", "properties": {"condition": {"$ref": "Expr", "description": "The condition that is associated with this binding. If the condition evaluates to `true`, then this binding applies to the current request. If the condition evaluates to `false`, then this binding does not apply to the current request. However, a different role binding might grant the same role to one or more of the principals in this binding. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies)."}, "members": {"description": "Specifies the principals requesting access for a Google Cloud resource. `members` can have the following values: * `allUsers`: A special identifier that represents anyone who is on the internet; with or without a Google account. * `allAuthenticatedUsers`: A special identifier that represents anyone who is authenticated with a Google account or a service account. Does not include identities that come from external identity providers (IdPs) through identity federation. * `user:{emailid}`: An email address that represents a specific Google account. For example, `<EMAIL>` . * `serviceAccount:{emailid}`: An email address that represents a Google service account. For example, `<EMAIL>`. * `serviceAccount:{projectid}.svc.id.goog[{namespace}/{kubernetes-sa}]`: An identifier for a [Kubernetes service account](https://cloud.google.com/kubernetes-engine/docs/how-to/kubernetes-service-accounts). For example, `my-project.svc.id.goog[my-namespace/my-kubernetes-sa]`. * `group:{emailid}`: An email address that represents a Google group. For example, `<EMAIL>`. * `domain:{domain}`: The G Suite domain (primary) that represents all the users of that domain. For example, `google.com` or `example.com`. * `principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workforce identity pool. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/group/{group_id}`: All workforce identities in a group. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All workforce identities with a specific attribute value. * `principalSet://iam.googleapis.com/locations/global/workforcePools/{pool_id}/*`: All identities in a workforce identity pool. * `principal://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/subject/{subject_attribute_value}`: A single identity in a workload identity pool. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/group/{group_id}`: A workload identity pool group. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/attribute.{attribute_name}/{attribute_value}`: All identities in a workload identity pool with a certain attribute. * `principalSet://iam.googleapis.com/projects/{project_number}/locations/global/workloadIdentityPools/{pool_id}/*`: All identities in a workload identity pool. * `deleted:user:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a user that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the user is recovered, this value reverts to `user:{emailid}` and the recovered user retains the role in the binding. * `deleted:serviceAccount:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a service account that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the service account is undeleted, this value reverts to `serviceAccount:{emailid}` and the undeleted service account retains the role in the binding. * `deleted:group:{emailid}?uid={uniqueid}`: An email address (plus unique identifier) representing a Google group that has been recently deleted. For example, `<EMAIL>?uid=123456789012345678901`. If the group is recovered, this value reverts to `group:{emailid}` and the recovered group retains the role in the binding. * `deleted:principal://iam.googleapis.com/locations/global/workforcePools/{pool_id}/subject/{subject_attribute_value}`: Deleted single identity in a workforce identity pool. For example, `deleted:principal://iam.googleapis.com/locations/global/workforcePools/my-pool-id/subject/my-subject-attribute-value`.", "items": {"type": "string"}, "type": "array"}, "role": {"description": "Role that is assigned to the list of `members`, or principals. For example, `roles/viewer`, `roles/editor`, or `roles/owner`. For an overview of the IAM roles and permissions, see the [IAM documentation](https://cloud.google.com/iam/docs/roles-overview). For a list of the available pre-defined roles, see [here](https://cloud.google.com/iam/docs/understanding-roles).", "type": "string"}}, "type": "object"}, "CancelOperationRequest": {"description": "The request message for Operations.CancelOperation.", "id": "CancelOperationRequest", "properties": {}, "type": "object"}, "DeleteStatefileRequest": {"description": "A request to delete a state file passed to a 'DeleteStatefile' call.", "id": "DeleteStatefileRequest", "properties": {"lockId": {"description": "Required. Lock ID of the lock file to verify that the user who is deleting the state file previously locked the Deployment.", "format": "int64", "type": "string"}}, "type": "object"}, "Deployment": {"description": "A Deployment is a group of resources and configs managed and provisioned by Infra Manager.", "id": "Deployment", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Arbitrary key-value metadata storage e.g. to help client tools identify deployments during automation. See https://google.aip.dev/148#annotations for details on format and size limitations.", "type": "object"}, "artifactsGcsBucket": {"description": "Optional. User-defined location of Cloud Build logs and artifacts in Google Cloud Storage. Format: `gs://{bucket}/{folder}` A default bucket will be bootstrapped if the field is not set or empty. Default bucket format: `gs://--blueprint-config` Constraints: - The bucket needs to be in the same project as the deployment - The path cannot be within the path of `gcs_source` - The field cannot be updated, including changing its presence", "type": "string"}, "createTime": {"description": "Output only. Time when the deployment was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deleteBuild": {"description": "Output only. Cloud Build instance UUID associated with deleting this deployment.", "readOnly": true, "type": "string"}, "deleteLogs": {"description": "Output only. Location of Cloud Build logs in Google Cloud Storage, populated when deleting this deployment. Format: `gs://{bucket}/{object}`.", "readOnly": true, "type": "string"}, "deleteResults": {"$ref": "ApplyResults", "description": "Output only. Location of artifacts from a DeleteDeployment operation.", "readOnly": true}, "errorCode": {"description": "Output only. Error code describing errors that may have occurred.", "enum": ["ERROR_CODE_UNSPECIFIED", "REVISION_FAILED", "CLOUD_BUILD_PERMISSION_DENIED", "DELETE_BUILD_API_FAILED", "DELETE_BUILD_RUN_FAILED", "BUCKET_CREATION_PERMISSION_DENIED", "BUCKET_CREATION_FAILED"], "enumDescriptions": ["No error code was specified.", "The revision failed. See Revision for more details.", "Cloud Build failed due to a permission issue.", "Cloud Build job associated with a deployment deletion could not be started.", "Cloud Build job associated with a deployment deletion was started but failed.", "Cloud Storage bucket creation failed due to a permission issue.", "Cloud Storage bucket creation failed due to an issue unrelated to permissions."], "readOnly": true, "type": "string"}, "errorLogs": {"description": "Output only. Location of Terraform error logs in Google Cloud Storage. Format: `gs://{bucket}/{object}`.", "readOnly": true, "type": "string"}, "importExistingResources": {"description": "By default, Infra Manager will return a failure when Terraform encounters a 409 code (resource conflict error) during actuation. If this flag is set to true, Infra Manager will instead attempt to automatically import the resource into the Terraform state (for supported resource types) and continue actuation. Not all resource types are supported, refer to documentation.", "type": "boolean"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. User-defined metadata for the deployment.", "type": "object"}, "latestRevision": {"description": "Output only. Revision name that was most recently applied. Format: `projects/{project}/locations/{location}/deployments/{deployment}/ revisions/{revision}`", "readOnly": true, "type": "string"}, "lockState": {"description": "Output only. Current lock state of the deployment.", "enum": ["LOCK_STATE_UNSPECIFIED", "LOCKED", "UNLOCKED", "LOCKING", "UNLOCKING", "LOCK_FAILED", "UNLOCK_FAILED"], "enumDescriptions": ["The default value. This value is used if the lock state is omitted.", "The deployment is locked.", "The deployment is unlocked.", "The deployment is being locked.", "The deployment is being unlocked.", "The deployment has failed to lock.", "The deployment has failed to unlock."], "readOnly": true, "type": "string"}, "name": {"description": "Identifier. Resource name of the deployment. Format: `projects/{project}/locations/{location}/deployments/{deployment}`", "type": "string"}, "quotaValidation": {"description": "Optional. Input to control quota checks for resources in terraform configuration files. There are limited resources on which quota validation applies.", "enum": ["QUOTA_VALIDATION_UNSPECIFIED", "ENABLED", "ENFORCED"], "enumDescriptions": ["The default value. QuotaValidation on terraform configuration files will be disabled in this case.", "Enable computing quotas for resources in terraform configuration files to get visibility on resources with insufficient quotas.", "Enforce quota checks so deployment fails if there isn't sufficient quotas available to deploy resources in terraform configuration files."], "type": "string"}, "serviceAccount": {"description": "Required. User-specified Service Account (SA) credentials to be used when actuating resources. Format: `projects/{projectID}/serviceAccounts/{serviceAccount}`", "type": "string"}, "state": {"description": "Output only. Current state of the deployment.", "enum": ["STATE_UNSPECIFIED", "CREATING", "ACTIVE", "UPDATING", "DELETING", "FAILED", "SUSPENDED", "DELETED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The deployment is being created.", "The deployment is healthy.", "The deployment is being updated.", "The deployment is being deleted.", "The deployment has encountered an unexpected error.", "The deployment is no longer being actively reconciled. This may be the result of recovering the project after deletion.", "The deployment has been deleted."], "readOnly": true, "type": "string"}, "stateDetail": {"description": "Output only. Additional information regarding the current state.", "readOnly": true, "type": "string"}, "terraformBlueprint": {"$ref": "TerraformBlueprint", "description": "A blueprint described using Terraform's HashiCorp Configuration Language as a root module."}, "tfErrors": {"description": "Output only. Errors encountered when deleting this deployment. Errors are truncated to 10 entries, see `delete_results` and `error_logs` for full details.", "items": {"$ref": "TerraformError"}, "readOnly": true, "type": "array"}, "tfVersion": {"description": "Output only. The current Terraform version set on the deployment. It is in the format of \"Major.Minor.Patch\", for example, \"1.3.10\".", "readOnly": true, "type": "string"}, "tfVersionConstraint": {"description": "Optional. The user-specified Terraform version constraint. Example: \"=1.3.10\".", "type": "string"}, "updateTime": {"description": "Output only. Time when the deployment was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}, "workerPool": {"description": "Optional. The user-specified Cloud Build worker pool resource in which the Cloud Build job will execute. Format: `projects/{project}/locations/{location}/workerPools/{workerPoolId}`. If this field is unspecified, the default Cloud Build worker pool will be used.", "type": "string"}}, "type": "object"}, "DeploymentOperationMetadata": {"description": "Ephemeral metadata content describing the state of a deployment operation.", "id": "DeploymentOperationMetadata", "properties": {"applyResults": {"$ref": "ApplyResults", "description": "Outputs and artifacts from applying a deployment."}, "build": {"description": "Output only. Cloud Build instance UUID associated with this operation.", "readOnly": true, "type": "string"}, "logs": {"description": "Output only. Location of Deployment operations logs in `gs://{bucket}/{object}` format.", "readOnly": true, "type": "string"}, "step": {"description": "The current step the deployment operation is running.", "enum": ["DEPLOYMENT_STEP_UNSPECIFIED", "PREPARING_STORAGE_BUCKET", "DOWNLOADING_BLUEPRINT", "RUNNING_TF_INIT", "RUNNING_TF_PLAN", "RUNNING_TF_APPLY", "RUNNING_TF_DESTROY", "RUNNING_TF_VALIDATE", "UNLOCKING_DEPLOYMENT", "SUCCEEDED", "FAILED", "VALIDATING_REPOSITORY", "RUNNING_QUOTA_VALIDATION"], "enumDescriptions": ["Unspecified deployment step", "Infra Manager is creating a Google Cloud Storage bucket to store artifacts and metadata about the deployment and revision", "Downloading the blueprint onto the Google Cloud Storage bucket", "Initializing Terraform using `terraform init`", "Running `terraform plan`", "Actuating resources using Terraform using `terraform apply`", "Destroying resources using Terraform using `terraform destroy`", "Validating the uploaded TF state file when unlocking a deployment", "Unlocking a deployment", "Operation was successful", "Operation failed", "Validating the provided repository.", "Running quota validation"], "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ExportDeploymentStatefileRequest": {"description": "A request to export a state file passed to a 'ExportDeploymentStatefile' call.", "id": "ExportDeploymentStatefileRequest", "properties": {"draft": {"description": "Optional. If this flag is set to true, the exported deployment state file will be the draft state. This will enable the draft file to be validated before copying it over to the working state on unlock.", "type": "boolean"}}, "type": "object"}, "ExportPreviewResultRequest": {"description": "A request to export preview results.", "id": "ExportPreviewResultRequest", "properties": {}, "type": "object"}, "ExportPreviewResultResponse": {"description": "A response to `ExportPreviewResult` call. Contains preview results.", "id": "ExportPreviewResultResponse", "properties": {"result": {"$ref": "PreviewResult", "description": "Output only. Signed URLs for accessing the plan files.", "readOnly": true}}, "type": "object"}, "ExportRevisionStatefileRequest": {"description": "A request to export a state file passed to a 'ExportRevisionStatefile' call.", "id": "ExportRevisionStatefileRequest", "properties": {}, "type": "object"}, "Expr": {"description": "Represents a textual expression in the Common Expression Language (CEL) syntax. CEL is a C-like expression language. The syntax and semantics of CEL are documented at https://github.com/google/cel-spec. Example (Comparison): title: \"Summary size limit\" description: \"Determines if a summary is less than 100 chars\" expression: \"document.summary.size() < 100\" Example (Equality): title: \"Requestor is owner\" description: \"Determines if requestor is the document owner\" expression: \"document.owner == request.auth.claims.email\" Example (Logic): title: \"Public documents\" description: \"Determine whether the document should be publicly visible\" expression: \"document.type != 'private' && document.type != 'internal'\" Example (Data Manipulation): title: \"Notification string\" description: \"Create a notification string with a timestamp.\" expression: \"'New message received at ' + string(document.create_time)\" The exact variables and functions that may be referenced within an expression are determined by the service that evaluates it. See the service documentation for additional information.", "id": "Expr", "properties": {"description": {"description": "Optional. Description of the expression. This is a longer text which describes the expression, e.g. when hovered over it in a UI.", "type": "string"}, "expression": {"description": "Textual representation of an expression in Common Expression Language syntax.", "type": "string"}, "location": {"description": "Optional. String indicating the location of the expression for error reporting, e.g. a file name and a position in the file.", "type": "string"}, "title": {"description": "Optional. Title for the expression, i.e. a short string describing its purpose. This can be used e.g. in UIs which allow to enter the expression.", "type": "string"}}, "type": "object"}, "GitSource": {"description": "A set of files in a Git repository.", "id": "GitSource", "properties": {"directory": {"description": "Optional. Subdirectory inside the repository. Example: 'staging/my-package'", "type": "string"}, "ref": {"description": "Optional. Git reference (e.g. branch or tag).", "type": "string"}, "repo": {"description": "Optional. Repository URL. Example: 'https://github.com/kubernetes/examples.git'", "type": "string"}}, "type": "object"}, "ImportStatefileRequest": {"description": "A request to import a state file passed to a 'ImportStatefile' call.", "id": "ImportStatefileRequest", "properties": {"lockId": {"description": "Required. Lock ID of the lock file to verify that the user who is importing the state file previously locked the Deployment.", "format": "int64", "type": "string"}}, "type": "object"}, "ListDeploymentsResponse": {"id": "ListDeploymentsResponse", "properties": {"deployments": {"description": "List of Deployments.", "items": {"$ref": "Deployment"}, "type": "array"}, "nextPageToken": {"description": "Token to be supplied to the next ListDeployments request via `page_token` to obtain the next set of results.", "type": "string"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "ListPreviewsResponse": {"description": "A response to a `ListPreviews` call. Contains a list of Previews.", "id": "ListPreviewsResponse", "properties": {"nextPageToken": {"description": "Token to be supplied to the next ListPreviews request via `page_token` to obtain the next set of results.", "type": "string"}, "previews": {"description": "List of Previews.", "items": {"$ref": "Preview"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListResourcesResponse": {"description": "A response to a 'ListResources' call. Contains a list of Resources.", "id": "ListResourcesResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the 'ListResources' method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "resources": {"description": "List of Resources.", "items": {"$ref": "Resource"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListRevisionsResponse": {"description": "A response to a 'ListRevisions' call. Contains a list of Revisions.", "id": "ListRevisionsResponse", "properties": {"nextPageToken": {"description": "A token to request the next page of resources from the 'ListRevisions' method. The value of an empty string means that there are no more resources to return.", "type": "string"}, "revisions": {"description": "List of Revisions.", "items": {"$ref": "Revision"}, "type": "array"}, "unreachable": {"description": "Locations that could not be reached.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ListTerraformVersionsResponse": {"description": "The response message for the `ListTerraformVersions` method.", "id": "ListTerraformVersionsResponse", "properties": {"nextPageToken": {"description": "Token to be supplied to the next ListTerraformVersions request via `page_token` to obtain the next set of results.", "type": "string"}, "terraformVersions": {"description": "List of TerraformVersions.", "items": {"$ref": "TerraformVersion"}, "type": "array"}, "unreachable": {"description": "Unreachable resources, if any.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "LockDeploymentRequest": {"description": "A request to lock a deployment passed to a 'LockDeployment' call.", "id": "LockDeploymentRequest", "properties": {}, "type": "object"}, "LockInfo": {"description": "Details about the lock which locked the deployment.", "id": "LockInfo", "properties": {"createTime": {"description": "Time that the lock was taken.", "format": "google-datetime", "type": "string"}, "info": {"description": "Extra information to store with the lock, provided by the caller.", "type": "string"}, "lockId": {"description": "Unique ID for the lock to be overridden with generation ID in the backend.", "format": "int64", "type": "string"}, "operation": {"description": "Terraform operation, provided by the caller.", "type": "string"}, "version": {"description": "Terraform version", "type": "string"}, "who": {"description": "user@hostname when available", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Represents the metadata of the long-running operation.", "id": "OperationMetadata", "properties": {"apiVersion": {"description": "Output only. API version used to start the operation.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time when the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deploymentMetadata": {"$ref": "DeploymentOperationMetadata", "description": "Output only. Metadata about the deployment operation state.", "readOnly": true}, "endTime": {"description": "Output only. Time when the operation finished running.", "format": "google-datetime", "readOnly": true, "type": "string"}, "previewMetadata": {"$ref": "PreviewOperationMetadata", "description": "Output only. Metadata about the preview operation state.", "readOnly": true}, "requestedCancellation": {"description": "Output only. Identifies whether the user has requested cancellation of the operation. Operations that have successfully been cancelled have google.longrunning.Operation.error value with a google.rpc.Status.code of `1`, corresponding to `Code.CANCELLED`.", "readOnly": true, "type": "boolean"}, "statusMessage": {"description": "Output only. Human-readable status of the operation, if any.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Server-defined resource path for the target of the operation.", "readOnly": true, "type": "string"}, "verb": {"description": "Output only. Name of the verb executed by the operation.", "readOnly": true, "type": "string"}}, "type": "object"}, "Policy": {"description": "An Identity and Access Management (IAM) policy, which specifies access controls for Google Cloud resources. A `Policy` is a collection of `bindings`. A `binding` binds one or more `members`, or principals, to a single `role`. Principals can be user accounts, service accounts, Google groups, and domains (such as G Suite). A `role` is a named list of permissions; each `role` can be an IAM predefined role or a user-created custom role. For some types of Google Cloud resources, a `binding` can also specify a `condition`, which is a logical expression that allows access to a resource only if the expression evaluates to `true`. A condition can add constraints based on attributes of the request, the resource, or both. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies). **JSON example:** ``` { \"bindings\": [ { \"role\": \"roles/resourcemanager.organizationAdmin\", \"members\": [ \"user:<EMAIL>\", \"group:<EMAIL>\", \"domain:google.com\", \"serviceAccount:<EMAIL>\" ] }, { \"role\": \"roles/resourcemanager.organizationViewer\", \"members\": [ \"user:<EMAIL>\" ], \"condition\": { \"title\": \"expirable access\", \"description\": \"Does not grant access after Sep 2020\", \"expression\": \"request.time < timestamp('2020-10-01T00:00:00.000Z')\", } } ], \"etag\": \"BwWWja0YfJA=\", \"version\": 3 } ``` **YAML example:** ``` bindings: - members: - user:<EMAIL> - group:<EMAIL> - domain:google.com - serviceAccount:<EMAIL> role: roles/resourcemanager.organizationAdmin - members: - user:<EMAIL> role: roles/resourcemanager.organizationViewer condition: title: expirable access description: Does not grant access after Sep 2020 expression: request.time < timestamp('2020-10-01T00:00:00.000Z') etag: BwWWja0YfJA= version: 3 ``` For a description of IAM and its features, see the [IAM documentation](https://cloud.google.com/iam/docs/).", "id": "Policy", "properties": {"auditConfigs": {"description": "Specifies cloud audit logging configuration for this policy.", "items": {"$ref": "AuditConfig"}, "type": "array"}, "bindings": {"description": "Associates a list of `members`, or principals, with a `role`. Optionally, may specify a `condition` that determines how and when the `bindings` are applied. Each of the `bindings` must contain at least one principal. The `bindings` in a `Policy` can refer to up to 1,500 principals; up to 250 of these principals can be Google groups. Each occurrence of a principal counts towards these limits. For example, if the `bindings` grant 50 different roles to `user:<EMAIL>`, and not to any other principal, then you can add another 1,450 principals to the `bindings` in the `Policy`.", "items": {"$ref": "Binding"}, "type": "array"}, "etag": {"description": "`etag` is used for optimistic concurrency control as a way to help prevent simultaneous updates of a policy from overwriting each other. It is strongly suggested that systems make use of the `etag` in the read-modify-write cycle to perform policy updates in order to avoid race conditions: An `etag` is returned in the response to `getIamPolicy`, and systems are expected to put that etag in the request to `setIamPolicy` to ensure that their change will be applied to the same version of the policy. **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost.", "format": "byte", "type": "string"}, "version": {"description": "Specifies the format of the policy. Valid values are `0`, `1`, and `3`. Requests that specify an invalid value are rejected. Any operation that affects conditional role bindings must specify version `3`. This requirement applies to the following operations: * Getting a policy that includes a conditional role binding * Adding a conditional role binding to a policy * Changing a conditional role binding in a policy * Removing any role binding, with or without a condition, from a policy that includes conditions **Important:** If you use IAM Conditions, you must include the `etag` field whenever you call `setIamPolicy`. If you omit this field, then IAM allows you to overwrite a version `3` policy with a version `1` policy, and all of the conditions in the version `3` policy are lost. If a policy does not include any conditions, operations on that policy may specify any valid version or leave the field unset. To learn which resources support conditions in their IAM policies, see the [IAM documentation](https://cloud.google.com/iam/help/conditions/resource-policies).", "format": "int32", "type": "integer"}}, "type": "object"}, "Preview": {"description": "A preview represents a set of actions Infra Manager would perform to move the resources towards the desired state as specified in the configuration.", "id": "Preview", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "Optional. Arbitrary key-value metadata storage e.g. to help client tools identify preview during automation. See https://google.aip.dev/148#annotations for details on format and size limitations.", "type": "object"}, "artifactsGcsBucket": {"description": "Optional. User-defined location of Cloud Build logs, artifacts, and in Google Cloud Storage. Format: `gs://{bucket}/{folder}` A default bucket will be bootstrapped if the field is not set or empty Default Bucket Format: `gs://--blueprint-config` Constraints: - The bucket needs to be in the same project as the deployment - The path cannot be within the path of `gcs_source` If omitted and deployment resource ref provided has artifacts_gcs_bucket defined, that artifact bucket is used.", "type": "string"}, "build": {"description": "Output only. Cloud Build instance UUID associated with this preview.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time the preview was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "deployment": {"description": "Optional. Optional deployment reference. If specified, the preview will be performed using the provided deployment's current state and use any relevant fields from the deployment unless explicitly specified in the preview create request.", "type": "string"}, "errorCode": {"description": "Output only. Code describing any errors that may have occurred.", "enum": ["ERROR_CODE_UNSPECIFIED", "CLOUD_BUILD_PERMISSION_DENIED", "BUCKET_CREATION_PERMISSION_DENIED", "BUCKET_CREATION_FAILED", "DEPLOYMENT_LOCK_ACQUIRE_FAILED", "PREVIEW_BUILD_API_FAILED", "PREVIEW_BUILD_RUN_FAILED"], "enumDescriptions": ["No error code was specified.", "Cloud Build failed due to a permissions issue.", "Cloud Storage bucket failed to create due to a permissions issue.", "Cloud Storage bucket failed for a non-permissions-related issue.", "Acquiring lock on provided deployment reference failed.", "Preview encountered an error when trying to access Cloud Build API.", "Preview created a build but build failed and logs were generated."], "readOnly": true, "type": "string"}, "errorLogs": {"description": "Output only. Link to tf-error.ndjson file, which contains the full list of the errors encountered during a Terraform preview. Format: `gs://{bucket}/{object}`.", "readOnly": true, "type": "string"}, "errorStatus": {"$ref": "Status", "description": "Output only. Additional information regarding the current state.", "readOnly": true}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. User-defined labels for the preview.", "type": "object"}, "logs": {"description": "Output only. Location of preview logs in `gs://{bucket}/{object}` format.", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. Resource name of the preview. Resource name can be user provided or server generated ID if unspecified. Format: `projects/{project}/locations/{location}/previews/{preview}`", "type": "string"}, "previewArtifacts": {"$ref": "PreviewArtifacts", "description": "Output only. Artifacts from preview.", "readOnly": true}, "previewMode": {"description": "Optional. Current mode of preview.", "enum": ["PREVIEW_MODE_UNSPECIFIED", "DEFAULT", "DELETE"], "enumDescriptions": ["Unspecified policy, default mode will be used.", "DEFAULT mode generates an execution plan for reconciling current resource state into expected resource state.", "DELETE mode generates as execution plan for destroying current resources."], "type": "string"}, "serviceAccount": {"description": "Required. User-specified Service Account (SA) credentials to be used when previewing resources. Format: `projects/{projectID}/serviceAccounts/{serviceAccount}`", "type": "string"}, "state": {"description": "Output only. Current state of the preview.", "enum": ["STATE_UNSPECIFIED", "CREATING", "SUCCEEDED", "APPLYING", "STALE", "DELETING", "FAILED", "DELETED"], "enumDescriptions": ["The default value. This value is used if the state is unknown.", "The preview is being created.", "The preview has succeeded.", "The preview is being applied.", "The preview is stale. A preview can become stale if a revision has been applied after this preview was created.", "The preview is being deleted.", "The preview has encountered an unexpected error.", "The preview has been deleted."], "readOnly": true, "type": "string"}, "terraformBlueprint": {"$ref": "TerraformBlueprint", "description": "The terraform blueprint to preview."}, "tfErrors": {"description": "Output only. Summary of errors encountered during Terraform preview. It has a size limit of 10, i.e. only top 10 errors will be summarized here.", "items": {"$ref": "TerraformError"}, "readOnly": true, "type": "array"}, "tfVersion": {"description": "Output only. The current Terraform version set on the preview. It is in the format of \"Major.Minor.Patch\", for example, \"1.3.10\".", "readOnly": true, "type": "string"}, "tfVersionConstraint": {"description": "Optional. The user-specified Terraform version constraint. Example: \"=1.3.10\".", "type": "string"}, "workerPool": {"description": "Optional. The user-specified Worker Pool resource in which the Cloud Build job will execute. Format projects/{project}/locations/{location}/workerPools/{workerPoolId} If this field is unspecified, the default Cloud Build worker pool will be used. If omitted and deployment resource ref provided has worker_pool defined, that worker pool is used.", "type": "string"}}, "type": "object"}, "PreviewArtifacts": {"description": "Artifacts created by preview.", "id": "PreviewArtifacts", "properties": {"artifacts": {"description": "Output only. Location of artifacts in Google Cloud Storage. Format: `gs://{bucket}/{object}`", "readOnly": true, "type": "string"}, "content": {"description": "Output only. Location of a blueprint copy and other content in Google Cloud Storage. Format: `gs://{bucket}/{object}`", "readOnly": true, "type": "string"}}, "type": "object"}, "PreviewOperationMetadata": {"description": "Ephemeral metadata content describing the state of a preview operation.", "id": "PreviewOperationMetadata", "properties": {"build": {"description": "Output only. Cloud Build instance UUID associated with this preview.", "readOnly": true, "type": "string"}, "logs": {"description": "Output only. Location of preview logs in `gs://{bucket}/{object}` format.", "readOnly": true, "type": "string"}, "previewArtifacts": {"$ref": "PreviewArtifacts", "description": "Artifacts from preview."}, "step": {"description": "The current step the preview operation is running.", "enum": ["PREVIEW_STEP_UNSPECIFIED", "PREPARING_STORAGE_BUCKET", "DOWNLOADING_BLUEPRINT", "RUNNING_TF_INIT", "RUNNING_TF_PLAN", "FETCHING_DEPLOYMENT", "LOCKING_DEPLOYMENT", "UNLOCKING_DEPLOYMENT", "SUCCEEDED", "FAILED", "VALIDATING_REPOSITORY"], "enumDescriptions": ["Unspecified preview step.", "Infra Manager is creating a Google Cloud Storage bucket to store artifacts and metadata about the preview.", "Downloading the blueprint onto the Google Cloud Storage bucket.", "Initializing Terraform using `terraform init`.", "Running `terraform plan`.", "Fetching a deployment.", "Locking a deployment.", "Unlocking a deployment.", "Operation was successful.", "Operation failed.", "Validating the provided repository."], "type": "string"}}, "type": "object"}, "PreviewResult": {"description": "Contains a signed Cloud Storage URLs.", "id": "PreviewResult", "properties": {"binarySignedUri": {"description": "Output only. Plan binary signed URL", "readOnly": true, "type": "string"}, "jsonSignedUri": {"description": "Output only. Plan JSON signed URL", "readOnly": true, "type": "string"}}, "type": "object"}, "Resource": {"description": "Resource represents a Google Cloud Platform resource actuated by IM. Resources are child resources of Revisions.", "id": "Resource", "properties": {"caiAssets": {"additionalProperties": {"$ref": "ResourceCAIInfo"}, "description": "Output only. Map of Cloud Asset Inventory (CAI) type to CAI info (e.g. CAI ID). CAI type format follows https://cloud.google.com/asset-inventory/docs/supported-asset-types", "readOnly": true, "type": "object"}, "intent": {"description": "Output only. Intent of the resource.", "enum": ["INTENT_UNSPECIFIED", "CREATE", "UPDATE", "DELETE", "RECREATE", "UNCHANGED"], "enumDescriptions": ["The default value. This value is used if the intent is omitted.", "Infra Manager will create this Resource.", "Infra Manager will update this Resource.", "Infra Manager will delete this Resource.", "Infra Manager will destroy and recreate this Resource.", "Infra Manager will leave this Resource untouched."], "readOnly": true, "type": "string"}, "name": {"description": "Output only. Resource name. Format: `projects/{project}/locations/{location}/deployments/{deployment}/revisions/{revision}/resources/{resource}`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Current state of the resource.", "enum": ["STATE_UNSPECIFIED", "PLANNED", "IN_PROGRESS", "RECONCILED", "FAILED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "Resource has been planned for reconcile.", "Resource is actively reconciling into the intended state.", "Resource has reconciled to intended state.", "Resource failed to reconcile."], "readOnly": true, "type": "string"}, "terraformInfo": {"$ref": "ResourceTerraformInfo", "description": "Output only. Terraform-specific info if this resource was created using Terraform.", "readOnly": true}}, "type": "object"}, "ResourceCAIInfo": {"description": "CAI info of a Resource.", "id": "ResourceCAIInfo", "properties": {"fullResourceName": {"description": "CAI resource name in the format following https://cloud.google.com/apis/design/resource_names#full_resource_name", "type": "string"}}, "type": "object"}, "ResourceTerraformInfo": {"description": "Terraform info of a Resource.", "id": "ResourceTerraformInfo", "properties": {"address": {"description": "TF resource address that uniquely identifies this resource within this deployment.", "type": "string"}, "id": {"description": "ID attribute of the TF resource", "type": "string"}, "type": {"description": "TF resource type", "type": "string"}}, "type": "object"}, "Revision": {"description": "A child resource of a Deployment generated by a 'CreateDeployment' or 'UpdateDeployment' call. Each Revision contains metadata pertaining to a snapshot of a particular Deployment.", "id": "Revision", "properties": {"action": {"description": "Output only. The action which created this revision", "enum": ["ACTION_UNSPECIFIED", "CREATE", "UPDATE", "DELETE"], "enumDescriptions": ["The default value. This value is used if the action is omitted.", "The revision was generated by creating a deployment.", "The revision was generated by updating a deployment.", "The revision was deleted."], "readOnly": true, "type": "string"}, "applyResults": {"$ref": "ApplyResults", "description": "Output only. Outputs and artifacts from applying a deployment.", "readOnly": true}, "build": {"description": "Output only. Cloud Build instance UUID associated with this revision.", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. Time when the revision was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "errorCode": {"description": "Output only. Code describing any errors that may have occurred.", "enum": ["ERROR_CODE_UNSPECIFIED", "CLOUD_BUILD_PERMISSION_DENIED", "APPLY_BUILD_API_FAILED", "APPLY_BUILD_RUN_FAILED", "QUOTA_VALIDATION_FAILED"], "enumDescriptions": ["No error code was specified.", "Cloud Build failed due to a permission issue.", "Cloud Build job associated with creating or updating a deployment could not be started.", "Cloud Build job associated with creating or updating a deployment was started but failed.", "quota validation failed for one or more resources in terraform configuration files."], "readOnly": true, "type": "string"}, "errorLogs": {"description": "Output only. Location of Terraform error logs in Google Cloud Storage. Format: `gs://{bucket}/{object}`.", "readOnly": true, "type": "string"}, "importExistingResources": {"description": "Output only. By default, Infra Manager will return a failure when Terraform encounters a 409 code (resource conflict error) during actuation. If this flag is set to true, Infra Manager will instead attempt to automatically import the resource into the Terraform state (for supported resource types) and continue actuation. Not all resource types are supported, refer to documentation.", "readOnly": true, "type": "boolean"}, "logs": {"description": "Output only. Location of Revision operation logs in `gs://{bucket}/{object}` format.", "readOnly": true, "type": "string"}, "name": {"description": "Revision name. Format: `projects/{project}/locations/{location}/deployments/{deployment}/ revisions/{revision}`", "type": "string"}, "quotaValidation": {"description": "Optional. Input to control quota checks for resources in terraform configuration files. There are limited resources on which quota validation applies.", "enum": ["QUOTA_VALIDATION_UNSPECIFIED", "ENABLED", "ENFORCED"], "enumDescriptions": ["The default value. QuotaValidation on terraform configuration files will be disabled in this case.", "Enable computing quotas for resources in terraform configuration files to get visibility on resources with insufficient quotas.", "Enforce quota checks so deployment fails if there isn't sufficient quotas available to deploy resources in terraform configuration files."], "type": "string"}, "quotaValidationResults": {"description": "Output only. Cloud Storage path containing quota validation results. This field is set when a user sets Deployment.quota_validation field to ENABLED or ENFORCED. Format: `gs://{bucket}/{object}`.", "readOnly": true, "type": "string"}, "serviceAccount": {"description": "Output only. User-specified Service Account (SA) to be used as credential to manage resources. Format: `projects/{projectID}/serviceAccounts/{serviceAccount}`", "readOnly": true, "type": "string"}, "state": {"description": "Output only. Current state of the revision.", "enum": ["STATE_UNSPECIFIED", "APPLYING", "APPLIED", "FAILED"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The revision is being applied.", "The revision was applied successfully.", "The revision could not be applied successfully."], "readOnly": true, "type": "string"}, "stateDetail": {"description": "Output only. Additional info regarding the current state.", "readOnly": true, "type": "string"}, "terraformBlueprint": {"$ref": "TerraformBlueprint", "description": "Output only. A blueprint described using Terraform's HashiCorp Configuration Language as a root module.", "readOnly": true}, "tfErrors": {"description": "Output only. Errors encountered when creating or updating this deployment. Errors are truncated to 10 entries, see `delete_results` and `error_logs` for full details.", "items": {"$ref": "TerraformError"}, "readOnly": true, "type": "array"}, "tfVersion": {"description": "Output only. The version of Terraform used to create the Revision. It is in the format of \"Major.Minor.Patch\", for example, \"1.3.10\".", "readOnly": true, "type": "string"}, "tfVersionConstraint": {"description": "Output only. The user-specified Terraform version constraint. Example: \"=1.3.10\".", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. Time when the revision was last modified.", "format": "google-datetime", "readOnly": true, "type": "string"}, "workerPool": {"description": "Output only. The user-specified Cloud Build worker pool resource in which the Cloud Build job will execute. Format: `projects/{project}/locations/{location}/workerPools/{workerPoolId}`. If this field is unspecified, the default Cloud Build worker pool will be used.", "readOnly": true, "type": "string"}}, "type": "object"}, "SetIamPolicyRequest": {"description": "Request message for `SetIamPolicy` method.", "id": "SetIamPolicyRequest", "properties": {"policy": {"$ref": "Policy", "description": "REQUIRED: The complete policy to be applied to the `resource`. The size of the policy is limited to a few 10s of KB. An empty policy is a valid policy but certain Google Cloud services (such as Projects) might reject them."}, "updateMask": {"description": "OPTIONAL: A FieldMask specifying which fields of the policy to modify. Only the fields in the mask will be modified. If no mask is provided, the following default mask is used: `paths: \"bindings, etag\"`", "format": "google-fieldmask", "type": "string"}}, "type": "object"}, "Statefile": {"description": "Contains info about a Terraform state file", "id": "Statefile", "properties": {"signedUri": {"description": "Output only. Cloud Storage signed URI used for downloading or uploading the state file.", "readOnly": true, "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "TerraformBlueprint": {"description": "TerraformBlueprint describes the source of a Terraform root module which describes the resources and configs to be deployed.", "id": "TerraformBlueprint", "properties": {"gcsSource": {"description": "URI of an object in Google Cloud Storage. Format: `gs://{bucket}/{object}` URI may also specify an object version for zipped objects. Format: `gs://{bucket}/{object}#{version}`", "type": "string"}, "gitSource": {"$ref": "GitSource", "description": "URI of a public Git repo."}, "inputValues": {"additionalProperties": {"$ref": "TerraformVariable"}, "description": "Optional. Input variable values for the Terraform blueprint.", "type": "object"}}, "type": "object"}, "TerraformError": {"description": "Errors encountered during actuation using Terraform", "id": "TerraformError", "properties": {"error": {"$ref": "Status", "description": "Output only. Original error response from underlying Google API, if available.", "readOnly": true}, "errorDescription": {"description": "A human-readable error description.", "type": "string"}, "httpResponseCode": {"description": "HTTP response code returned from Google Cloud Platform APIs when Terraform fails to provision the resource. If unset or 0, no HTTP response code was returned by Terraform.", "format": "int32", "type": "integer"}, "resourceAddress": {"description": "Address of the resource associated with the error, e.g. `google_compute_network.vpc_network`.", "type": "string"}}, "type": "object"}, "TerraformOutput": {"description": "Describes a Terraform output.", "id": "TerraformOutput", "properties": {"sensitive": {"description": "Identifies whether Terraform has set this output as a potential sensitive value.", "type": "boolean"}, "value": {"description": "Value of output.", "type": "any"}}, "type": "object"}, "TerraformVariable": {"description": "A Terraform input variable.", "id": "TerraformVariable", "properties": {"inputValue": {"description": "Optional. Input variable value.", "type": "any"}}, "type": "object"}, "TerraformVersion": {"description": "A TerraformVersion represents the support state the corresponding Terraform version.", "id": "TerraformVersion", "properties": {"deprecateTime": {"description": "Output only. When the version is deprecated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The version name is in the format: 'projects/{project_id}/locations/{location}/terraformVersions/{terraform_version}'.", "type": "string"}, "obsoleteTime": {"description": "Output only. When the version is obsolete.", "format": "google-datetime", "readOnly": true, "type": "string"}, "state": {"description": "Output only. The state of the version, ACTIVE, DEPRECATED or OBSOLETE.", "enum": ["STATE_UNSPECIFIED", "ACTIVE", "DEPRECATED", "OBSOLETE"], "enumDescriptions": ["The default value. This value is used if the state is omitted.", "The version is actively supported.", "The version is deprecated.", "The version is obsolete."], "readOnly": true, "type": "string"}, "supportTime": {"description": "Output only. When the version is supported.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "TestIamPermissionsRequest": {"description": "Request message for `TestIamPermissions` method.", "id": "TestIamPermissionsRequest", "properties": {"permissions": {"description": "The set of permissions to check for the `resource`. Permissions with wildcards (such as `*` or `storage.*`) are not allowed. For more information see [IAM Overview](https://cloud.google.com/iam/docs/overview#permissions).", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "TestIamPermissionsResponse": {"description": "Response message for `TestIamPermissions` method.", "id": "TestIamPermissionsResponse", "properties": {"permissions": {"description": "A subset of `TestPermissionsRequest.permissions` that the caller is allowed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "UnlockDeploymentRequest": {"description": "A request to unlock a state file passed to a 'UnlockDeployment' call.", "id": "UnlockDeploymentRequest", "properties": {"lockId": {"description": "Required. Lock ID of the lock file to be unlocked.", "format": "int64", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Infrastructure Manager API", "version": "v1", "version_module": true}
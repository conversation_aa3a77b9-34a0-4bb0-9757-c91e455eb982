{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/adexchange.buyer": {"description": "Manage your Ad Exchange buyer account configuration"}}}}, "basePath": "", "baseUrl": "https://adexchangebuyer.googleapis.com/", "batchPath": "batch", "canonicalName": "AdExchangeBuyerII", "description": "Accesses the latest features for managing Authorized Buyers accounts, Real-Time Bidding configurations and auction metrics, and Marketplace programmatic deals.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/authorized-buyers/apis/reference/rest/", "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "adexchangebuyer2:v2beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://adexchangebuyer.mtls.googleapis.com/", "name": "adexchangebuyer2", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"clients": {"methods": {"create": {"description": "Creates a new client buyer.", "flatPath": "v2beta1/accounts/{accountId}/clients", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.clients.create", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Unique numerical account ID for the buyer of which the client buyer is a customer; the sponsor buyer to create a client for. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients", "request": {"$ref": "Client"}, "response": {"$ref": "Client"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"description": "Gets a client buyer with a given client account ID.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.clients.get", "parameterOrder": ["accountId", "clientAccountId"], "parameters": {"accountId": {"description": "Numerical account ID of the client's sponsor buyer. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "Numerical account ID of the client buyer to retrieve. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}", "response": {"$ref": "Client"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "Lists all the clients for the current sponsor buyer.", "flatPath": "v2beta1/accounts/{accountId}/clients", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.clients.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Unique numerical account ID of the sponsor buyer to list the clients for.", "format": "int64", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer clients than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListClientsResponse.nextPageToken returned from the previous call to the accounts.clients.list method.", "location": "query", "type": "string"}, "partnerClientId": {"description": "Optional unique identifier (from the standpoint of an Ad Exchange sponsor buyer partner) of the client to return. If specified, at most one client will be returned in the response.", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients", "response": {"$ref": "ListClientsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"description": "Updates an existing client buyer.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}", "httpMethod": "PUT", "id": "adexchangebuyer2.accounts.clients.update", "parameterOrder": ["accountId", "clientAccountId"], "parameters": {"accountId": {"description": "Unique numerical account ID for the buyer of which the client buyer is a customer; the sponsor buyer to update a client for. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "Unique numerical account ID of the client to update. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}", "request": {"$ref": "Client"}, "response": {"$ref": "Client"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"invitations": {"methods": {"create": {"description": "Creates and sends out an email invitation to access an Ad Exchange client buyer account.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/invitations", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.clients.invitations.create", "parameterOrder": ["accountId", "clientAccountId"], "parameters": {"accountId": {"description": "Numerical account ID of the client's sponsor buyer. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "Numerical account ID of the client buyer that the user should be associated with. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/invitations", "request": {"$ref": "ClientUserInvitation"}, "response": {"$ref": "ClientUserInvitation"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"description": "Retrieves an existing client user invitation.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/invitations/{invitationId}", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.clients.invitations.get", "parameterOrder": ["accountId", "clientAccountId", "invitationId"], "parameters": {"accountId": {"description": "Numerical account ID of the client's sponsor buyer. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "Numerical account ID of the client buyer that the user invitation to be retrieved is associated with. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "invitationId": {"description": "Numerical identifier of the user invitation to retrieve. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/invitations/{invitationId}", "response": {"$ref": "ClientUserInvitation"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "Lists all the client users invitations for a client with a given account ID.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/invitations", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.clients.invitations.list", "parameterOrder": ["accountId", "clientAccountId"], "parameters": {"accountId": {"description": "Numerical account ID of the client's sponsor buyer. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "Numerical account ID of the client buyer to list invitations for. (required) You must either specify a string representation of a numerical account identifier or the `-` character to list all the invitations for all the clients of a given sponsor buyer.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer clients than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListClientUserInvitationsResponse.nextPageToken returned from the previous call to the clients.invitations.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/invitations", "response": {"$ref": "ListClientUserInvitationsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "users": {"methods": {"get": {"description": "Retrieves an existing client user.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/users/{userId}", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.clients.users.get", "parameterOrder": ["accountId", "clientAccountId", "userId"], "parameters": {"accountId": {"description": "Numerical account ID of the client's sponsor buyer. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "Numerical account ID of the client buyer that the user to be retrieved is associated with. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "userId": {"description": "Numerical identifier of the user to retrieve. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/users/{userId}", "response": {"$ref": "ClientUser"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "Lists all the known client users for a specified sponsor buyer account ID.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/users", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.clients.users.list", "parameterOrder": ["accountId", "clientAccountId"], "parameters": {"accountId": {"description": "Numerical account ID of the sponsor buyer of the client to list users for. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "The account ID of the client buyer to list users for. (required) You must specify either a string representation of a numerical account identifier or the `-` character to list all the client users for all the clients of a given sponsor buyer.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer clients than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListClientUsersResponse.nextPageToken returned from the previous call to the accounts.clients.users.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/users", "response": {"$ref": "ListClientUsersResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"description": "Updates an existing client user. Only the user status can be changed on update.", "flatPath": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/users/{userId}", "httpMethod": "PUT", "id": "adexchangebuyer2.accounts.clients.users.update", "parameterOrder": ["accountId", "clientAccountId", "userId"], "parameters": {"accountId": {"description": "Numerical account ID of the client's sponsor buyer. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "clientAccountId": {"description": "Numerical account ID of the client buyer that the user to be retrieved is associated with. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}, "userId": {"description": "Numerical identifier of the user to retrieve. (required)", "format": "int64", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/clients/{clientAccountId}/users/{userId}", "request": {"$ref": "ClientUser"}, "response": {"$ref": "ClientUser"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}, "creatives": {"methods": {"create": {"description": "Creates a creative.", "flatPath": "v2beta1/accounts/{accountId}/creatives", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.creatives.create", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "The account that this creative belongs to. Can be used to filter the response of the creatives.list method.", "location": "path", "required": true, "type": "string"}, "duplicateIdMode": {"description": "Indicates if multiple creatives can share an ID or not. Default is NO_DUPLICATES (one ID per creative).", "enum": ["NO_DUPLICATES", "FORCE_ENABLE_DUPLICATE_IDS"], "enumDescriptions": ["Recommended. This means that an ID will be unique to a single creative. Multiple creatives will not share an ID.", "Not recommended. Using this option will allow multiple creatives to share the same ID. Get and Update requests will not be possible for any ID that has more than one creative associated. (List will still function.) This is only intended for backwards compatibility in cases where a single ID is already shared by multiple creatives from previous APIs."], "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives", "request": {"$ref": "Creative"}, "response": {"$ref": "Creative"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"description": "Gets a creative.", "flatPath": "v2beta1/accounts/{accountId}/creatives/{creativeId}", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.creatives.get", "parameterOrder": ["accountId", "creativeId"], "parameters": {"accountId": {"description": "The account the creative belongs to.", "location": "path", "required": true, "type": "string"}, "creativeId": {"description": "The ID of the creative to retrieve.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives/{creativeId}", "response": {"$ref": "Creative"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "Lists creatives.", "flatPath": "v2beta1/accounts/{accountId}/creatives", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.creatives.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "The account to list the creatives from. Specify \"-\" to list all creatives the current user has access to.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer creatives than requested (due to timeout constraint) even if more are available through another call. If unspecified, server will pick an appropriate default. Acceptable values are 1 to 1000, inclusive.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListCreativesResponse.next_page_token returned from the previous call to 'ListCreatives' method.", "location": "query", "type": "string"}, "query": {"description": "An optional query string to filter creatives. If no filter is specified, all active creatives will be returned. Supported queries are: - accountId=*account_id_string* - creativeId=*creative_id_string* - dealsStatus: {approved, conditionally_approved, disapproved, not_checked} - openAuctionStatus: {approved, conditionally_approved, disapproved, not_checked} - attribute: {a numeric attribute from the list of attributes} - disapprovalReason: {a reason from DisapprovalReason} Example: 'accountId=12345 AND (dealsStatus:disapproved AND disapprovalReason:unacceptable_content) OR attribute:47'", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives", "response": {"$ref": "ListCreativesResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "stopWatching": {"description": "Stops watching a creative. Will stop push notifications being sent to the topics when the creative changes status.", "flatPath": "v2beta1/accounts/{accountId}/creatives/{creativeId}:stopWatching", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.creatives.stopWatching", "parameterOrder": ["accountId", "creativeId"], "parameters": {"accountId": {"description": "The account of the creative to stop notifications for.", "location": "path", "required": true, "type": "string"}, "creativeId": {"description": "The creative ID of the creative to stop notifications for. Specify \"-\" to specify stopping account level notifications.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives/{creativeId}:stopWatching", "request": {"$ref": "StopWatchingCreativeRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"description": "Updates a creative.", "flatPath": "v2beta1/accounts/{accountId}/creatives/{creativeId}", "httpMethod": "PUT", "id": "adexchangebuyer2.accounts.creatives.update", "parameterOrder": ["accountId", "creativeId"], "parameters": {"accountId": {"description": "The account that this creative belongs to. Can be used to filter the response of the creatives.list method.", "location": "path", "required": true, "type": "string"}, "creativeId": {"description": "The buyer-defined creative ID of this creative. Can be used to filter the response of the creatives.list method.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives/{creativeId}", "request": {"$ref": "Creative"}, "response": {"$ref": "Creative"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "watch": {"description": "Watches a creative. Will result in push notifications being sent to the topic when the creative changes status.", "flatPath": "v2beta1/accounts/{accountId}/creatives/{creativeId}:watch", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.creatives.watch", "parameterOrder": ["accountId", "creativeId"], "parameters": {"accountId": {"description": "The account of the creative to watch.", "location": "path", "required": true, "type": "string"}, "creativeId": {"description": "The creative ID to watch for status changes. Specify \"-\" to watch all creatives under the above account. If both creative-level and account-level notifications are sent, only a single notification will be sent to the creative-level notification topic.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives/{creativeId}:watch", "request": {"$ref": "WatchCreativeRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"dealAssociations": {"methods": {"add": {"description": "Associate an existing deal with a creative.", "flatPath": "v2beta1/accounts/{accountId}/creatives/{creativeId}/dealAssociations:add", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.creatives.dealAssociations.add", "parameterOrder": ["accountId", "creativeId"], "parameters": {"accountId": {"description": "The account the creative belongs to.", "location": "path", "required": true, "type": "string"}, "creativeId": {"description": "The ID of the creative associated with the deal.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives/{creativeId}/dealAssociations:add", "request": {"$ref": "AddDealAssociationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "List all creative-deal associations.", "flatPath": "v2beta1/accounts/{accountId}/creatives/{creativeId}/dealAssociations", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.creatives.dealAssociations.list", "parameterOrder": ["accountId", "creativeId"], "parameters": {"accountId": {"description": "The account to list the associations from. Specify \"-\" to list all creatives the current user has access to.", "location": "path", "required": true, "type": "string"}, "creativeId": {"description": "The creative ID to list the associations from. Specify \"-\" to list all creatives under the above account.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. Server may return fewer associations than requested. If unspecified, server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListDealAssociationsResponse.next_page_token returned from the previous call to 'ListDealAssociations' method.", "location": "query", "type": "string"}, "query": {"description": "An optional query string to filter deal associations. If no filter is specified, all associations will be returned. Supported queries are: - accountId=*account_id_string* - creativeId=*creative_id_string* - dealsId=*deals_id_string* - dealsStatus:{approved, conditionally_approved, disapproved, not_checked} - openAuctionStatus:{approved, conditionally_approved, disapproved, not_checked} Example: 'dealsId=12345 AND dealsStatus:disapproved'", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives/{creativeId}/dealAssociations", "response": {"$ref": "ListDealAssociationsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "remove": {"description": "Remove the association between a deal and a creative.", "flatPath": "v2beta1/accounts/{accountId}/creatives/{creativeId}/dealAssociations:remove", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.creatives.dealAssociations.remove", "parameterOrder": ["accountId", "creativeId"], "parameters": {"accountId": {"description": "The account the creative belongs to.", "location": "path", "required": true, "type": "string"}, "creativeId": {"description": "The ID of the creative associated with the deal.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/creatives/{creativeId}/dealAssociations:remove", "request": {"$ref": "RemoveDealAssociationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}, "finalizedProposals": {"methods": {"list": {"description": "List finalized proposals, regardless if a proposal is being renegotiated. A filter expression (PQL query) may be specified to filter the results. The notes will not be returned.", "flatPath": "v2beta1/accounts/{accountId}/finalizedProposals", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.finalizedProposals.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "filter": {"description": "An optional PQL filter query used to query for proposals. Nested repeated fields, such as proposal.deals.targetingCriterion, cannot be filtered.", "location": "query", "type": "string"}, "filterSyntax": {"description": "Syntax the filter is written in. Current implementation defaults to PQL but in the future it will be LIST_FILTER.", "enum": ["FILTER_SYNTAX_UNSPECIFIED", "PQL", "LIST_FILTER"], "enumDescriptions": ["A placeholder for an undefined filter syntax.", "PQL query syntax. Visit https://developers.google.com/ad-manager/api/pqlreference for PQL documentation and examples.", "API list filtering syntax. Read about syntax and usage at https://developers.google.com/authorized-buyers/apis/guides/v2/list-filters."], "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token as returned from ListProposalsResponse.", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/finalizedProposals", "response": {"$ref": "ListProposalsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "pause": {"description": "Update given deals to pause serving. This method will set the `DealServingMetadata.DealPauseStatus.has_buyer_paused` bit to true for all listed deals in the request. Currently, this method only applies to PG and PD deals. For PA deals, call accounts.proposals.pause endpoint. It is a no-op to pause already-paused deals. It is an error to call PauseProposalDeals for deals which are not part of the proposal of proposal_id or which are not finalized or renegotiating.", "flatPath": "v2beta1/accounts/{accountId}/finalizedProposals/{proposalId}:pause", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.finalizedProposals.pause", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The proposal_id of the proposal containing the deals.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/finalizedProposals/{proposalId}:pause", "request": {"$ref": "PauseProposalDealsRequest"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "resume": {"description": "Update given deals to resume serving. This method will set the `DealServingMetadata.DealPauseStatus.has_buyer_paused` bit to false for all listed deals in the request. Currently, this method only applies to PG and PD deals. For PA deals, call accounts.proposals.resume endpoint. It is a no-op to resume running deals or deals paused by the other party. It is an error to call ResumeProposalDeals for deals which are not part of the proposal of proposal_id or which are not finalized or renegotiating.", "flatPath": "v2beta1/accounts/{accountId}/finalizedProposals/{proposalId}:resume", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.finalizedProposals.resume", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The proposal_id of the proposal containing the deals.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/finalizedProposals/{proposalId}:resume", "request": {"$ref": "ResumeProposalDealsRequest"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "products": {"methods": {"get": {"description": "Gets the requested product by ID.", "flatPath": "v2beta1/accounts/{accountId}/products/{productId}", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.products.get", "parameterOrder": ["accountId", "productId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "productId": {"description": "The ID for the product to get the head revision for.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/products/{productId}", "response": {"$ref": "Product"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "List all products visible to the buyer (optionally filtered by the specified PQL query).", "flatPath": "v2beta1/accounts/{accountId}/products", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.products.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "filter": {"description": "An optional PQL query used to query for products. See https://developers.google.com/ad-manager/docs/pqlreference for documentation about PQL and examples. Nested repeated fields, such as product.targetingCriterion.inclusions, cannot be filtered.", "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token as returned from ListProductsResponse.", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/products", "response": {"$ref": "ListProductsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "proposals": {"methods": {"accept": {"description": "Mark the proposal as accepted at the given revision number. If the number does not match the server's revision number an `ABORTED` error message will be returned. This call updates the proposal_state from `PROPOSED` to `BUYER_ACCEPTED`, or from `SELLER_ACCEPTED` to `FINALIZED`. Upon calling this endpoint, the buyer implicitly agrees to the terms and conditions optionally set within the proposal by the publisher.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}:accept", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.proposals.accept", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The ID of the proposal to accept.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}:accept", "request": {"$ref": "AcceptProposalRequest"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "addNote": {"description": "Create a new note and attach it to the proposal. The note is assigned a unique ID by the server. The proposal revision number will not increase when associated with a new note.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}:addNote", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.proposals.addNote", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The ID of the proposal to attach the note to.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}:addNote", "request": {"$ref": "AddNoteRequest"}, "response": {"$ref": "Note"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "cancelNegotiation": {"description": "Cancel an ongoing negotiation on a proposal. This does not cancel or end serving for the deals if the proposal has been finalized, but only cancels a negotiation unilaterally.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}:cancelNegotiation", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.proposals.cancelNegotiation", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The ID of the proposal to cancel negotiation for.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}:cancelNegotiation", "request": {"$ref": "CancelNegotiationRequest"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "completeSetup": {"description": "You can opt-in to manually update proposals to indicate that setup is complete. By default, proposal setup is automatically completed after their deals are finalized. Contact your Technical Account Manager to opt in. Buyers can call this method when the proposal has been finalized, and all the required creatives have been uploaded using the Creatives API. This call updates the `is_setup_completed` field on the deals in the proposal, and notifies the seller. The server then advances the revision number of the most recent proposal. To mark an individual deal as ready to serve, call `buyers.finalizedDeals.setReadyToServe` in the Marketplace API.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}:completeSetup", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.proposals.completeSetup", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The ID of the proposal to mark as setup completed.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}:completeSetup", "request": {"$ref": "CompleteSetupRequest"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "create": {"description": "Create the given proposal. Each created proposal and any deals it contains are assigned a unique ID by the server.", "flatPath": "v2beta1/accounts/{accountId}/proposals", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.proposals.create", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals", "request": {"$ref": "Proposal"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"description": "Gets a proposal given its ID. The proposal is returned at its head revision.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.proposals.get", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The unique ID of the proposal", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}", "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "List proposals. A filter expression (PQL query) may be specified to filter the results. To retrieve all finalized proposals, regardless if a proposal is being renegotiated, see the FinalizedProposals resource. Note that Bidder/ChildSeat relationships differ from the usual behavior. A Bidder account can only see its child seats' proposals by specifying the ChildSeat's accountId in the request path.", "flatPath": "v2beta1/accounts/{accountId}/proposals", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.proposals.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "filter": {"description": "An optional PQL filter query used to query for proposals. Nested repeated fields, such as proposal.deals.targetingCriterion, cannot be filtered.", "location": "query", "type": "string"}, "filterSyntax": {"description": "Syntax the filter is written in. Current implementation defaults to PQL but in the future it will be LIST_FILTER.", "enum": ["FILTER_SYNTAX_UNSPECIFIED", "PQL", "LIST_FILTER"], "enumDescriptions": ["A placeholder for an undefined filter syntax.", "PQL query syntax. Visit https://developers.google.com/ad-manager/api/pqlreference for PQL documentation and examples.", "API list filtering syntax. Read about syntax and usage at https://developers.google.com/authorized-buyers/apis/guides/v2/list-filters."], "location": "query", "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token as returned from ListProposalsResponse.", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals", "response": {"$ref": "ListProposalsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "pause": {"description": "Update the given proposal to pause serving. This method will set the `DealServingMetadata.DealPauseStatus.has_buyer_paused` bit to true for all deals in the proposal. It is a no-op to pause an already-paused proposal. It is an error to call PauseProposal for a proposal that is not finalized or renegotiating.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}:pause", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.proposals.pause", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The ID of the proposal to pause.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}:pause", "request": {"$ref": "PauseProposalRequest"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "resume": {"description": "Update the given proposal to resume serving. This method will set the `DealServingMetadata.DealPauseStatus.has_buyer_paused` bit to false for all deals in the proposal. Note that if the `has_seller_paused` bit is also set, serving will not resume until the seller also resumes. It is a no-op to resume an already-running proposal. It is an error to call ResumeProposal for a proposal that is not finalized or renegotiating.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}:resume", "httpMethod": "POST", "id": "adexchangebuyer2.accounts.proposals.resume", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The ID of the proposal to resume.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}:resume", "request": {"$ref": "ResumeProposalRequest"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "update": {"description": "Update the given proposal at the client known revision number. If the server revision has advanced since the passed-in `proposal.proposal_revision`, an `ABORTED` error message will be returned. Only the buyer-modifiable fields of the proposal will be updated. Note that the deals in the proposal will be updated to match the passed-in copy. If a passed-in deal does not have a `deal_id`, the server will assign a new unique ID and create the deal. If passed-in deal has a `deal_id`, it will be updated to match the passed-in copy. Any existing deals not present in the passed-in proposal will be deleted. It is an error to pass in a deal with a `deal_id` not present at head.", "flatPath": "v2beta1/accounts/{accountId}/proposals/{proposalId}", "httpMethod": "PUT", "id": "adexchangebuyer2.accounts.proposals.update", "parameterOrder": ["accountId", "proposalId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "proposalId": {"description": "The unique ID of the proposal.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/proposals/{proposalId}", "request": {"$ref": "Proposal"}, "response": {"$ref": "Proposal"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "publisherProfiles": {"methods": {"get": {"description": "Gets the requested publisher profile by id.", "flatPath": "v2beta1/accounts/{accountId}/publisherProfiles/{publisherProfileId}", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.publisherProfiles.get", "parameterOrder": ["accountId", "publisherProfileId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "publisherProfileId": {"description": "The id for the publisher profile to get.", "location": "path", "required": true, "type": "string"}}, "path": "v2beta1/accounts/{accountId}/publisherProfiles/{publisherProfileId}", "response": {"$ref": "PublisherProfile"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "List all publisher profiles visible to the buyer", "flatPath": "v2beta1/accounts/{accountId}/publisherProfiles", "httpMethod": "GET", "id": "adexchangebuyer2.accounts.publisherProfiles.list", "parameterOrder": ["accountId"], "parameters": {"accountId": {"description": "Account ID of the buyer.", "location": "path", "required": true, "type": "string"}, "pageSize": {"description": "Specify the number of results to include per page.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The page token as return from ListPublisherProfilesResponse.", "location": "query", "type": "string"}}, "path": "v2beta1/accounts/{accountId}/publisherProfiles", "response": {"$ref": "ListPublisherProfilesResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}, "bidders": {"resources": {"accounts": {"resources": {"filterSets": {"methods": {"create": {"description": "Creates the specified filter set for the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets", "httpMethod": "POST", "id": "adexchangebuyer2.bidders.accounts.filterSets.create", "parameterOrder": ["ownerName"], "parameters": {"isTransient": {"description": "Whether the filter set is transient, or should be persisted indefinitely. By default, filter sets are not transient. If transient, it will be available for at least 1 hour after creation.", "location": "query", "type": "boolean"}, "ownerName": {"description": "Name of the owner (bidder or account) of the filter set to be created. For example: - For a bidder-level filter set for bidder 123: `bidders/123` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+ownerName}/filterSets", "request": {"$ref": "FilterSet"}, "response": {"$ref": "FilterSet"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "delete": {"description": "Deletes the requested filter set from the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}", "httpMethod": "DELETE", "id": "adexchangebuyer2.bidders.accounts.filterSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Full name of the resource to delete. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"description": "Retrieves the requested filter set for the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Full name of the resource being requested. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "FilterSet"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "Lists all filter sets for the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.list", "parameterOrder": ["ownerName"], "parameters": {"ownerName": {"description": "Name of the owner (bidder or account) of the filter sets to be listed. For example: - For a bidder-level filter set for bidder 123: `bidders/123` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilterSetsResponse.nextPageToken returned from the previous call to the accounts.filterSets.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+ownerName}/filterSets", "response": {"$ref": "ListFilterSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"bidMetrics": {"methods": {"list": {"description": "Lists all metrics that are measured in terms of number of bids.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/bidMetrics", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.bidMetrics.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidMetricsResponse.nextPageToken returned from the previous call to the bidMetrics.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidMetrics", "response": {"$ref": "ListBidMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "bidResponseErrors": {"methods": {"list": {"description": "List all errors that occurred in bid responses, with the number of bid responses affected for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/bidResponseErrors", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.bidResponseErrors.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidResponseErrorsResponse.nextPageToken returned from the previous call to the bidResponseErrors.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidResponseErrors", "response": {"$ref": "ListBidResponseErrorsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "bidResponsesWithoutBids": {"methods": {"list": {"description": "List all reasons for which bid responses were considered to have no applicable bids, with the number of bid responses affected for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/bidResponsesWithoutBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.bidResponsesWithoutBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidResponsesWithoutBidsResponse.nextPageToken returned from the previous call to the bidResponsesWithoutBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidResponsesWithoutBids", "response": {"$ref": "ListBidResponsesWithoutBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "filteredBidRequests": {"methods": {"list": {"description": "List all reasons that caused a bid request not to be sent for an impression, with the number of bid requests not sent for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/filteredBidRequests", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.filteredBidRequests.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilteredBidRequestsResponse.nextPageToken returned from the previous call to the filteredBidRequests.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBidRequests", "response": {"$ref": "ListFilteredBidRequestsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "filteredBids": {"methods": {"list": {"description": "List all reasons for which bids were filtered, with the number of bids filtered for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/filteredBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.filteredBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilteredBidsResponse.nextPageToken returned from the previous call to the filteredBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids", "response": {"$ref": "ListFilteredBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"creatives": {"methods": {"list": {"description": "List all creatives associated with a specific reason for which bids were filtered, with the number of bids filtered for each creative.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/filteredBids/{creativeStatusId}/creatives", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.filteredBids.creatives.list", "parameterOrder": ["filterSetName", "creativeStatusId"], "parameters": {"creativeStatusId": {"description": "The ID of the creative status for which to retrieve a breakdown by creative. See [creative-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/creative-status-codes).", "format": "int32", "location": "path", "required": true, "type": "integer"}, "filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListCreativeStatusBreakdownByCreativeResponse.nextPageToken returned from the previous call to the filteredBids.creatives.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids/{creativeStatusId}/creatives", "response": {"$ref": "ListCreativeStatusBreakdownByCreativeResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "details": {"methods": {"list": {"description": "List all details associated with a specific reason for which bids were filtered, with the number of bids filtered for each detail.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/filteredBids/{creativeStatusId}/details", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.filteredBids.details.list", "parameterOrder": ["filterSetName", "creativeStatusId"], "parameters": {"creativeStatusId": {"description": "The ID of the creative status for which to retrieve a breakdown by detail. See [creative-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/creative-status-codes). Details are only available for statuses 10, 14, 15, 17, 18, 19, 86, and 87.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListCreativeStatusBreakdownByDetailResponse.nextPageToken returned from the previous call to the filteredBids.details.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids/{creativeStatusId}/details", "response": {"$ref": "ListCreativeStatusBreakdownByDetailResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}, "impressionMetrics": {"methods": {"list": {"description": "Lists all metrics that are measured in terms of number of impressions.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/impressionMetrics", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.impressionMetrics.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListImpressionMetricsResponse.nextPageToken returned from the previous call to the impressionMetrics.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/impressionMetrics", "response": {"$ref": "ListImpressionMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "losingBids": {"methods": {"list": {"description": "List all reasons for which bids lost in the auction, with the number of bids that lost for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/losingBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.losingBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListLosingBidsResponse.nextPageToken returned from the previous call to the losingBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/losingBids", "response": {"$ref": "ListLosingBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "nonBillableWinningBids": {"methods": {"list": {"description": "List all reasons for which winning bids were not billable, with the number of bids not billed for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/accounts/{accountsId}/filterSets/{filterSetsId}/nonBillableWinningBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.accounts.filterSets.nonBillableWinningBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/accounts/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListNonBillableWinningBidsResponse.nextPageToken returned from the previous call to the nonBillableWinningBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/nonBillableWinningBids", "response": {"$ref": "ListNonBillableWinningBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}}}, "filterSets": {"methods": {"create": {"description": "Creates the specified filter set for the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets", "httpMethod": "POST", "id": "adexchangebuyer2.bidders.filterSets.create", "parameterOrder": ["ownerName"], "parameters": {"isTransient": {"description": "Whether the filter set is transient, or should be persisted indefinitely. By default, filter sets are not transient. If transient, it will be available for at least 1 hour after creation.", "location": "query", "type": "boolean"}, "ownerName": {"description": "Name of the owner (bidder or account) of the filter set to be created. For example: - For a bidder-level filter set for bidder 123: `bidders/123` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456`", "location": "path", "pattern": "^bidders/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+ownerName}/filterSets", "request": {"$ref": "FilterSet"}, "response": {"$ref": "FilterSet"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "delete": {"description": "Deletes the requested filter set from the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}", "httpMethod": "DELETE", "id": "adexchangebuyer2.bidders.filterSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Full name of the resource to delete. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"description": "Retrieves the requested filter set for the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Full name of the resource being requested. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "FilterSet"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "Lists all filter sets for the account with the given account ID.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.list", "parameterOrder": ["ownerName"], "parameters": {"ownerName": {"description": "Name of the owner (bidder or account) of the filter sets to be listed. For example: - For a bidder-level filter set for bidder 123: `bidders/123` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456`", "location": "path", "pattern": "^bidders/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilterSetsResponse.nextPageToken returned from the previous call to the accounts.filterSets.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+ownerName}/filterSets", "response": {"$ref": "ListFilterSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"bidMetrics": {"methods": {"list": {"description": "Lists all metrics that are measured in terms of number of bids.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/bidMetrics", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.bidMetrics.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidMetricsResponse.nextPageToken returned from the previous call to the bidMetrics.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidMetrics", "response": {"$ref": "ListBidMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "bidResponseErrors": {"methods": {"list": {"description": "List all errors that occurred in bid responses, with the number of bid responses affected for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/bidResponseErrors", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.bidResponseErrors.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidResponseErrorsResponse.nextPageToken returned from the previous call to the bidResponseErrors.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidResponseErrors", "response": {"$ref": "ListBidResponseErrorsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "bidResponsesWithoutBids": {"methods": {"list": {"description": "List all reasons for which bid responses were considered to have no applicable bids, with the number of bid responses affected for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/bidResponsesWithoutBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.bidResponsesWithoutBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidResponsesWithoutBidsResponse.nextPageToken returned from the previous call to the bidResponsesWithoutBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidResponsesWithoutBids", "response": {"$ref": "ListBidResponsesWithoutBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "filteredBidRequests": {"methods": {"list": {"description": "List all reasons that caused a bid request not to be sent for an impression, with the number of bid requests not sent for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/filteredBidRequests", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.filteredBidRequests.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilteredBidRequestsResponse.nextPageToken returned from the previous call to the filteredBidRequests.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBidRequests", "response": {"$ref": "ListFilteredBidRequestsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "filteredBids": {"methods": {"list": {"description": "List all reasons for which bids were filtered, with the number of bids filtered for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/filteredBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.filteredBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilteredBidsResponse.nextPageToken returned from the previous call to the filteredBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids", "response": {"$ref": "ListFilteredBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"creatives": {"methods": {"list": {"description": "List all creatives associated with a specific reason for which bids were filtered, with the number of bids filtered for each creative.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/filteredBids/{creativeStatusId}/creatives", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.filteredBids.creatives.list", "parameterOrder": ["filterSetName", "creativeStatusId"], "parameters": {"creativeStatusId": {"description": "The ID of the creative status for which to retrieve a breakdown by creative. See [creative-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/creative-status-codes).", "format": "int32", "location": "path", "required": true, "type": "integer"}, "filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListCreativeStatusBreakdownByCreativeResponse.nextPageToken returned from the previous call to the filteredBids.creatives.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids/{creativeStatusId}/creatives", "response": {"$ref": "ListCreativeStatusBreakdownByCreativeResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "details": {"methods": {"list": {"description": "List all details associated with a specific reason for which bids were filtered, with the number of bids filtered for each detail.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/filteredBids/{creativeStatusId}/details", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.filteredBids.details.list", "parameterOrder": ["filterSetName", "creativeStatusId"], "parameters": {"creativeStatusId": {"description": "The ID of the creative status for which to retrieve a breakdown by detail. See [creative-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/creative-status-codes). Details are only available for statuses 10, 14, 15, 17, 18, 19, 86, and 87.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListCreativeStatusBreakdownByDetailResponse.nextPageToken returned from the previous call to the filteredBids.details.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids/{creativeStatusId}/details", "response": {"$ref": "ListCreativeStatusBreakdownByDetailResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}, "impressionMetrics": {"methods": {"list": {"description": "Lists all metrics that are measured in terms of number of impressions.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/impressionMetrics", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.impressionMetrics.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListImpressionMetricsResponse.nextPageToken returned from the previous call to the impressionMetrics.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/impressionMetrics", "response": {"$ref": "ListImpressionMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "losingBids": {"methods": {"list": {"description": "List all reasons for which bids lost in the auction, with the number of bids that lost for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/losingBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.losingBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListLosingBidsResponse.nextPageToken returned from the previous call to the losingBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/losingBids", "response": {"$ref": "ListLosingBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "nonBillableWinningBids": {"methods": {"list": {"description": "List all reasons for which winning bids were not billable, with the number of bids not billed for each reason.", "flatPath": "v2beta1/bidders/{biddersId}/filterSets/{filterSetsId}/nonBillableWinningBids", "httpMethod": "GET", "id": "adexchangebuyer2.bidders.filterSets.nonBillableWinningBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^bidders/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListNonBillableWinningBidsResponse.nextPageToken returned from the previous call to the nonBillableWinningBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/nonBillableWinningBids", "response": {"$ref": "ListNonBillableWinningBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}}}, "buyers": {"resources": {"filterSets": {"methods": {"create": {"description": "Creates the specified filter set for the account with the given account ID.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets", "httpMethod": "POST", "id": "adexchangebuyer2.buyers.filterSets.create", "parameterOrder": ["ownerName"], "parameters": {"isTransient": {"description": "Whether the filter set is transient, or should be persisted indefinitely. By default, filter sets are not transient. If transient, it will be available for at least 1 hour after creation.", "location": "query", "type": "boolean"}, "ownerName": {"description": "Name of the owner (bidder or account) of the filter set to be created. For example: - For a bidder-level filter set for bidder 123: `bidders/123` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456`", "location": "path", "pattern": "^buyers/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+ownerName}/filterSets", "request": {"$ref": "FilterSet"}, "response": {"$ref": "FilterSet"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "delete": {"description": "Deletes the requested filter set from the account with the given account ID.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}", "httpMethod": "DELETE", "id": "adexchangebuyer2.buyers.filterSets.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Full name of the resource to delete. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "get": {"description": "Retrieves the requested filter set for the account with the given account ID.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Full name of the resource being requested. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}}, "path": "v2beta1/{+name}", "response": {"$ref": "FilterSet"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}, "list": {"description": "Lists all filter sets for the account with the given account ID.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.list", "parameterOrder": ["ownerName"], "parameters": {"ownerName": {"description": "Name of the owner (bidder or account) of the filter sets to be listed. For example: - For a bidder-level filter set for bidder 123: `bidders/123` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456`", "location": "path", "pattern": "^buyers/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilterSetsResponse.nextPageToken returned from the previous call to the accounts.filterSets.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+ownerName}/filterSets", "response": {"$ref": "ListFilterSetsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"bidMetrics": {"methods": {"list": {"description": "Lists all metrics that are measured in terms of number of bids.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/bidMetrics", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.bidMetrics.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidMetricsResponse.nextPageToken returned from the previous call to the bidMetrics.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidMetrics", "response": {"$ref": "ListBidMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "bidResponseErrors": {"methods": {"list": {"description": "List all errors that occurred in bid responses, with the number of bid responses affected for each reason.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/bidResponseErrors", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.bidResponseErrors.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidResponseErrorsResponse.nextPageToken returned from the previous call to the bidResponseErrors.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidResponseErrors", "response": {"$ref": "ListBidResponseErrorsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "bidResponsesWithoutBids": {"methods": {"list": {"description": "List all reasons for which bid responses were considered to have no applicable bids, with the number of bid responses affected for each reason.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/bidResponsesWithoutBids", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.bidResponsesWithoutBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListBidResponsesWithoutBidsResponse.nextPageToken returned from the previous call to the bidResponsesWithoutBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/bidResponsesWithoutBids", "response": {"$ref": "ListBidResponsesWithoutBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "filteredBidRequests": {"methods": {"list": {"description": "List all reasons that caused a bid request not to be sent for an impression, with the number of bid requests not sent for each reason.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/filteredBidRequests", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.filteredBidRequests.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilteredBidRequestsResponse.nextPageToken returned from the previous call to the filteredBidRequests.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBidRequests", "response": {"$ref": "ListFilteredBidRequestsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "filteredBids": {"methods": {"list": {"description": "List all reasons for which bids were filtered, with the number of bids filtered for each reason.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/filteredBids", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.filteredBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListFilteredBidsResponse.nextPageToken returned from the previous call to the filteredBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids", "response": {"$ref": "ListFilteredBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}, "resources": {"creatives": {"methods": {"list": {"description": "List all creatives associated with a specific reason for which bids were filtered, with the number of bids filtered for each creative.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/filteredBids/{creativeStatusId}/creatives", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.filteredBids.creatives.list", "parameterOrder": ["filterSetName", "creativeStatusId"], "parameters": {"creativeStatusId": {"description": "The ID of the creative status for which to retrieve a breakdown by creative. See [creative-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/creative-status-codes).", "format": "int32", "location": "path", "required": true, "type": "integer"}, "filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListCreativeStatusBreakdownByCreativeResponse.nextPageToken returned from the previous call to the filteredBids.creatives.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids/{creativeStatusId}/creatives", "response": {"$ref": "ListCreativeStatusBreakdownByCreativeResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "details": {"methods": {"list": {"description": "List all details associated with a specific reason for which bids were filtered, with the number of bids filtered for each detail.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/filteredBids/{creativeStatusId}/details", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.filteredBids.details.list", "parameterOrder": ["filterSetName", "creativeStatusId"], "parameters": {"creativeStatusId": {"description": "The ID of the creative status for which to retrieve a breakdown by detail. See [creative-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/creative-status-codes). Details are only available for statuses 10, 14, 15, 17, 18, 19, 86, and 87.", "format": "int32", "location": "path", "required": true, "type": "integer"}, "filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListCreativeStatusBreakdownByDetailResponse.nextPageToken returned from the previous call to the filteredBids.details.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/filteredBids/{creativeStatusId}/details", "response": {"$ref": "ListCreativeStatusBreakdownByDetailResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}, "impressionMetrics": {"methods": {"list": {"description": "Lists all metrics that are measured in terms of number of impressions.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/impressionMetrics", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.impressionMetrics.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListImpressionMetricsResponse.nextPageToken returned from the previous call to the impressionMetrics.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/impressionMetrics", "response": {"$ref": "ListImpressionMetricsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "losingBids": {"methods": {"list": {"description": "List all reasons for which bids lost in the auction, with the number of bids that lost for each reason.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/losingBids", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.losingBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListLosingBidsResponse.nextPageToken returned from the previous call to the losingBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/losingBids", "response": {"$ref": "ListLosingBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}, "nonBillableWinningBids": {"methods": {"list": {"description": "List all reasons for which winning bids were not billable, with the number of bids not billed for each reason.", "flatPath": "v2beta1/buyers/{buyersId}/filterSets/{filterSetsId}/nonBillableWinningBids", "httpMethod": "GET", "id": "adexchangebuyer2.buyers.filterSets.nonBillableWinningBids.list", "parameterOrder": ["filterSetName"], "parameters": {"filterSetName": {"description": "Name of the filter set that should be applied to the requested metrics. For example: - For a bidder-level filter set for bidder 123: `bidders/123/filterSets/abc` - For an account-level filter set for the buyer account representing bidder 123: `bidders/123/accounts/123/filterSets/abc` - For an account-level filter set for the child seat buyer account 456 whose bidder is 123: `bidders/123/accounts/456/filterSets/abc`", "location": "path", "pattern": "^buyers/[^/]+/filterSets/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "Requested page size. The server may return fewer results than requested. If unspecified, the server will pick an appropriate default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A token identifying a page of results the server should return. Typically, this is the value of ListNonBillableWinningBidsResponse.nextPageToken returned from the previous call to the nonBillableWinningBids.list method.", "location": "query", "type": "string"}}, "path": "v2beta1/{+filterSetName}/nonBillableWinningBids", "response": {"$ref": "ListNonBillableWinningBidsResponse"}, "scopes": ["https://www.googleapis.com/auth/adexchange.buyer"]}}}}}}}}, "revision": "********", "rootUrl": "https://adexchangebuyer.googleapis.com/", "schemas": {"AbsoluteDateRange": {"description": "An absolute date range, specified by its start date and end date. The supported range of dates begins 30 days before today and ends today. Validity checked upon filter set creation. If a filter set with an absolute date range is run at a later date more than 30 days after start_date, it will fail.", "id": "AbsoluteDateRange", "properties": {"endDate": {"$ref": "Date", "description": "The end date of the range (inclusive). Must be within the 30 days leading up to current date, and must be equal to or after start_date."}, "startDate": {"$ref": "Date", "description": "The start date of the range (inclusive). Must be within the 30 days leading up to current date, and must be equal to or before end_date."}}, "type": "object"}, "AcceptProposalRequest": {"description": "Request to accept a proposal.", "id": "AcceptProposalRequest", "properties": {"proposalRevision": {"description": "The last known client revision number of the proposal.", "format": "int64", "type": "string"}}, "type": "object"}, "AdSize": {"description": "Represents size of a single ad slot, or a creative.", "id": "AdSize", "properties": {"height": {"description": "The height of the ad slot in pixels. This field will be present only when size type is `PIXEL`.", "format": "int64", "type": "string"}, "sizeType": {"description": "The size type of the ad slot.", "enum": ["SIZE_TYPE_UNSPECIFIED", "PIXEL", "INTERSTITIAL", "NATIVE", "FLUID"], "enumDescriptions": ["A placeholder for an undefined size type.", "Ad slot with size specified by height and width in pixels.", "Special size to describe an interstitial ad slot.", "Native (mobile) ads rendered by the publisher.", "Fluid size (for example, responsive size) can be resized automatically with the change of outside environment."], "type": "string"}, "width": {"description": "The width of the ad slot in pixels. This field will be present only when size type is `PIXEL`.", "format": "int64", "type": "string"}}, "type": "object"}, "AdTechnologyProviders": {"description": "Detected ad technology provider information.", "id": "AdTechnologyProviders", "properties": {"detectedProviderIds": {"description": "The detected ad technology provider IDs for this creative. See https://storage.googleapis.com/adx-rtb-dictionaries/providers.csv for mapping of provider ID to provided name, a privacy policy URL, and a list of domains which can be attributed to the provider. If the creative contains provider IDs that are outside of those listed in the `BidRequest.adslot.consented_providers_settings.consented_providers` field on the (Google bid protocol)[https://developers.google.com/authorized-buyers/rtb/downloads/realtime-bidding-proto] and the `BidRequest.user.ext.consented_providers_settings.consented_providers` field on the (OpenRTB protocol)[https://developers.google.com/authorized-buyers/rtb/downloads/openrtb-adx-proto], and a bid is submitted with that creative for an impression that will serve to an EEA user, the bid will be filtered before the auction.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "hasUnidentifiedProvider": {"description": "Whether the creative contains an unidentified ad technology provider. If true for a given creative, any bid submitted with that creative for an impression that will serve to an EEA user will be filtered before the auction.", "type": "boolean"}}, "type": "object"}, "AddDealAssociationRequest": {"description": "A request for associating a deal and a creative.", "id": "AddDealAssociationRequest", "properties": {"association": {"$ref": "CreativeDealAssociation", "description": "The association between a creative and a deal that should be added."}}, "type": "object"}, "AddNoteRequest": {"description": "Request message for adding a note to a given proposal.", "id": "AddNoteRequest", "properties": {"note": {"$ref": "Note", "description": "Details of the note to add."}}, "type": "object"}, "AppContext": {"description": "Output only. The app type the restriction applies to for mobile device.", "id": "AppContext", "properties": {"appTypes": {"description": "The app types this restriction applies to.", "items": {"enum": ["NATIVE", "WEB"], "enumDescriptions": ["Native app context.", "Mobile web app context."], "type": "string"}, "type": "array"}}, "type": "object"}, "AuctionContext": {"description": "Output only. The auction type the restriction applies to.", "id": "AuctionContext", "properties": {"auctionTypes": {"description": "The auction types this restriction applies to.", "items": {"enum": ["OPEN_AUCTION", "DIRECT_DEALS"], "enumDescriptions": ["The restriction applies to open auction.", "The restriction applies to direct deals."], "type": "string"}, "type": "array"}}, "type": "object"}, "BidMetricsRow": {"description": "The set of metrics that are measured in numbers of bids, representing how many bids with the specified dimension values were considered eligible at each stage of the bidding funnel;", "id": "BidMetricsRow", "properties": {"bids": {"$ref": "MetricValue", "description": "The number of bids that Ad Exchange received from the buyer."}, "bidsInAuction": {"$ref": "MetricValue", "description": "The number of bids that were permitted to compete in the auction."}, "billedImpressions": {"$ref": "MetricValue", "description": "The number of bids for which the buyer was billed. Also called valid impressions as invalid impressions are not billed."}, "impressionsWon": {"$ref": "MetricValue", "description": "The number of bids that won the auction."}, "measurableImpressions": {"$ref": "MetricValue", "description": "The number of bids for which the corresponding impression was measurable for viewability (as defined by Active View)."}, "reachedQueries": {"$ref": "MetricValue", "description": "The number of bids that won the auction and also won the mediation waterfall (if any)."}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}, "viewableImpressions": {"$ref": "MetricValue", "description": "The number of bids for which the corresponding impression was viewable (as defined by Active View)."}}, "type": "object"}, "BidResponseWithoutBidsStatusRow": {"description": "The number of impressions with the specified dimension values that were considered to have no applicable bids, as described by the specified status.", "id": "BidResponseWithoutBidsStatusRow", "properties": {"impressionCount": {"$ref": "MetricValue", "description": "The number of impressions for which there was a bid response with the specified status."}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}, "status": {"description": "The status specifying why the bid responses were considered to have no applicable bids.", "enum": ["STATUS_UNSPECIFIED", "RESPONSES_WITHOUT_BIDS", "RESPONSES_WITHOUT_BIDS_FOR_ACCOUNT", "RESPONSES_WITHOUT_BIDS_FOR_DEAL"], "enumDescriptions": ["A placeholder for an undefined status. This value will never be returned in responses.", "The response had no bids.", "The response had no bids for the specified account, though it may have included bids on behalf of other accounts. Applies if: 1. Request is on behalf of a bidder and an account filter is present. 2. Request is on behalf of a child seat.", "The response had no bids for the specified deal, though it may have included bids on other deals on behalf of the account to which the deal belongs. If request is on behalf of a bidder and an account filter is not present, this also includes responses that have bids on behalf of accounts other than the account to which the deal belongs."], "type": "string"}}, "type": "object"}, "Buyer": {"description": "Represents a buyer of inventory. Each buyer is identified by a unique Authorized Buyers account ID.", "id": "Buyer", "properties": {"accountId": {"description": "Authorized Buyers account ID of the buyer.", "type": "string"}}, "type": "object"}, "CalloutStatusRow": {"description": "The number of impressions with the specified dimension values where the corresponding bid request or bid response was not successful, as described by the specified callout status.", "id": "CalloutStatusRow", "properties": {"calloutStatusId": {"description": "The ID of the callout status. See [callout-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/callout-status-codes).", "format": "int32", "type": "integer"}, "impressionCount": {"$ref": "MetricValue", "description": "The number of impressions for which there was a bid request or bid response with the specified callout status."}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}}, "type": "object"}, "CancelNegotiationRequest": {"description": "Request to cancel an ongoing negotiation.", "id": "CancelNegotiationRequest", "properties": {}, "type": "object"}, "Client": {"description": "A client resource represents a client buyer—an agency, a brand, or an advertiser customer of the sponsor buyer. Users associated with the client buyer have restricted access to the Marketplace and certain other sections of the Authorized Buyers UI based on the role granted to the client buyer. All fields are required unless otherwise specified.", "id": "Client", "properties": {"clientAccountId": {"description": "The globally-unique numerical ID of the client. The value of this field is ignored in create and update operations.", "format": "int64", "type": "string"}, "clientName": {"description": "Name used to represent this client to publishers. You may have multiple clients that map to the same entity, but for each client the combination of `clientName` and entity must be unique. You can specify this field as empty. Maximum length of 255 characters is allowed.", "type": "string"}, "entityId": {"description": "Numerical identifier of the client entity. The entity can be an advertiser, a brand, or an agency. This identifier is unique among all the entities with the same type. The value of this field is ignored if the entity type is not provided. A list of all known advertisers with their identifiers is available in the [advertisers.txt](https://storage.googleapis.com/adx-rtb-dictionaries/advertisers.txt) file. A list of all known brands with their identifiers is available in the [brands.txt](https://storage.googleapis.com/adx-rtb-dictionaries/brands.txt) file. A list of all known agencies with their identifiers is available in the [agencies.txt](https://storage.googleapis.com/adx-rtb-dictionaries/agencies.txt) file.", "format": "int64", "type": "string"}, "entityName": {"description": "The name of the entity. This field is automatically fetched based on the type and ID. The value of this field is ignored in create and update operations.", "type": "string"}, "entityType": {"description": "An optional field for specifying the type of the client entity: `ADVERTISER`, `BRAND`, or `AGENCY`.", "enum": ["ENTITY_TYPE_UNSPECIFIED", "ADVERTISER", "BRAND", "AGENCY", "ENTITY_TYPE_UNCLASSIFIED"], "enumDescriptions": ["A placeholder for an undefined client entity type. Should not be used.", "An advertiser.", "A brand.", "An advertising agency.", "An explicit value for a client that was not yet classified as any particular entity."], "type": "string"}, "partnerClientId": {"description": "Optional arbitrary unique identifier of this client buyer from the standpoint of its Ad Exchange sponsor buyer. This field can be used to associate a client buyer with the identifier in the namespace of its sponsor buyer, lookup client buyers by that identifier and verify whether an Ad Exchange counterpart of a given client buyer already exists. If present, must be unique among all the client buyers for its Ad Exchange sponsor buyer.", "type": "string"}, "role": {"description": "The role which is assigned to the client buyer. Each role implies a set of permissions granted to the client. Must be one of `CLIENT_DEAL_VIEWER`, `CLIENT_DEAL_NEGOTIATOR` or `CLIENT_DEAL_APPROVER`.", "enum": ["CLIENT_ROLE_UNSPECIFIED", "CLIENT_DEAL_VIEWER", "CLIENT_DEAL_NEGOTIATOR", "CLIENT_DEAL_APPROVER"], "enumDescriptions": ["A placeholder for an undefined client role.", "Users associated with this client can see publisher deal offers in the Marketplace. They can neither negotiate proposals nor approve deals. If this client is visible to publishers, they can send deal proposals to this client.", "Users associated with this client can respond to deal proposals sent to them by publishers. They can also initiate deal proposals of their own.", "Users associated with this client can approve eligible deals on your behalf. Some deals may still explicitly require publisher finalization. If this role is not selected, the sponsor buyer will need to manually approve each of their deals."], "type": "string"}, "status": {"description": "The status of the client buyer.", "enum": ["CLIENT_STATUS_UNSPECIFIED", "DISABLED", "ACTIVE"], "enumDescriptions": ["A placeholder for an undefined client status.", "A client that is currently disabled.", "A client that is currently active."], "type": "string"}, "visibleToSeller": {"description": "Whether the client buyer will be visible to sellers.", "type": "boolean"}}, "type": "object"}, "ClientUser": {"description": "A client user is created under a client buyer and has restricted access to the Marketplace and certain other sections of the Authorized Buyers UI based on the role granted to the associated client buyer. The only way a new client user can be created is through accepting an email invitation (see the accounts.clients.invitations.create method). All fields are required unless otherwise specified.", "id": "ClientUser", "properties": {"clientAccountId": {"description": "Numerical account ID of the client buyer with which the user is associated; the buyer must be a client of the current sponsor buyer. The value of this field is ignored in an update operation.", "format": "int64", "type": "string"}, "email": {"description": "User's email address. The value of this field is ignored in an update operation.", "type": "string"}, "status": {"description": "The status of the client user.", "enum": ["USER_STATUS_UNSPECIFIED", "PENDING", "ACTIVE", "DISABLED"], "enumDescriptions": ["A placeholder for an undefined user status.", "A user who was already created but hasn't accepted the invitation yet.", "A user that is currently active.", "A user that is currently disabled."], "type": "string"}, "userId": {"description": "The unique numerical ID of the client user that has accepted an invitation. The value of this field is ignored in an update operation.", "format": "int64", "type": "string"}}, "type": "object"}, "ClientUserInvitation": {"description": "An invitation for a new client user to get access to the Authorized Buyers UI. All fields are required unless otherwise specified.", "id": "ClientUserInvitation", "properties": {"clientAccountId": {"description": "Numerical account ID of the client buyer that the invited user is associated with. The value of this field is ignored in create operations.", "format": "int64", "type": "string"}, "email": {"description": "The email address to which the invitation is sent. Email addresses should be unique among all client users under each sponsor buyer.", "type": "string"}, "invitationId": {"description": "The unique numerical ID of the invitation that is sent to the user. The value of this field is ignored in create operations.", "format": "int64", "type": "string"}}, "type": "object"}, "CompleteSetupRequest": {"description": "Request message for indicating that the proposal's setup step is complete.", "id": "CompleteSetupRequest", "properties": {}, "type": "object"}, "ContactInformation": {"description": "Contains information on how a buyer or seller can be reached.", "id": "ContactInformation", "properties": {"email": {"description": "Email address for the contact.", "type": "string"}, "name": {"description": "The name of the contact.", "type": "string"}}, "type": "object"}, "Correction": {"description": "Output only. Shows any corrections that were applied to this creative.", "id": "Correction", "properties": {"contexts": {"description": "The contexts for the correction.", "items": {"$ref": "ServingContext"}, "type": "array"}, "details": {"description": "Additional details about what was corrected.", "items": {"type": "string"}, "type": "array"}, "type": {"description": "The type of correction that was applied to the creative.", "enum": ["CORRECTION_TYPE_UNSPECIFIED", "VENDOR_IDS_ADDED", "SSL_ATTRIBUTE_REMOVED", "FLASH_FREE_ATTRIBUTE_REMOVED", "FLASH_FREE_ATTRIBUTE_ADDED", "REQUIRED_ATTRIBUTE_ADDED", "REQUIRED_VENDOR_ADDED", "SSL_ATTRIBUTE_ADDED", "IN_BANNER_VIDEO_ATTRIBUTE_ADDED", "MRAID_ATTRIBUTE_ADDED", "FLASH_ATTRIBUTE_REMOVED", "VIDEO_IN_SNIPPET_ATTRIBUTE_ADDED"], "enumDescriptions": ["The correction type is unknown. Refer to the details for more information.", "The ad's declared vendors did not match the vendors that were detected. The detected vendors were added.", "The ad had the SSL attribute declared but was not SSL-compliant. The SSL attribute was removed.", "The ad was declared as Flash-free but contained Flash, so the Flash-free attribute was removed.", "The ad was not declared as Flash-free but it did not reference any flash content, so the Flash-free attribute was added.", "The ad did not declare a required creative attribute. The attribute was added.", "The ad did not declare a required technology vendor. The technology vendor was added.", "The ad did not declare the SSL attribute but was SSL-compliant, so the SSL attribute was added.", "Properties consistent with In-banner video were found, so an In-Banner Video attribute was added.", "The ad makes calls to the MRAID API so the MRAID attribute was added.", "The ad unnecessarily declared the Flash attribute, so the Flash attribute was removed.", "The ad contains video content."], "type": "string"}}, "type": "object"}, "Creative": {"description": "A creative and its classification data.", "id": "Creative", "properties": {"accountId": {"description": "The account that this creative belongs to. Can be used to filter the response of the creatives.list method.", "type": "string"}, "adChoicesDestinationUrl": {"description": "The link to AdChoices destination page.", "type": "string"}, "adTechnologyProviders": {"$ref": "AdTechnologyProviders", "description": "Output only. The detected ad technology providers."}, "advertiserName": {"description": "The name of the company being advertised in the creative.", "type": "string"}, "agencyId": {"description": "The agency ID for this creative.", "format": "int64", "type": "string"}, "apiUpdateTime": {"description": "Output only. The last update timestamp of the creative through the API.", "format": "google-datetime", "type": "string"}, "attributes": {"description": "All attributes for the ads that may be shown from this creative. Can be used to filter the response of the creatives.list method.", "items": {"enum": ["ATTRIBUTE_UNSPECIFIED", "IMAGE_RICH_MEDIA", "ADOBE_FLASH_FLV", "IS_TAGGED", "IS_COOKIE_TARGETED", "IS_USER_INTEREST_TARGETED", "EXPANDING_DIRECTION_NONE", "EXPANDING_DIRECTION_UP", "EXPANDING_DIRECTION_DOWN", "EXPANDING_DIRECTION_LEFT", "EXPANDING_DIRECTION_RIGHT", "EXPANDING_DIRECTION_UP_LEFT", "EXPANDING_DIRECTION_UP_RIGHT", "EXPANDING_DIRECTION_DOWN_LEFT", "EXPANDING_DIRECTION_DOWN_RIGHT", "CREATIVE_TYPE_HTML", "CREATIVE_TYPE_VAST_VIDEO", "EXPANDING_DIRECTION_UP_OR_DOWN", "EXPANDING_DIRECTION_LEFT_OR_RIGHT", "EXPANDING_DIRECTION_ANY_DIAGONAL", "EXPANDING_ACTION_ROLLOVER_TO_EXPAND", "INSTREAM_VAST_VIDEO_TYPE_VPAID_FLASH", "RICH_MEDIA_CAPABILITY_TYPE_MRAID", "RICH_MEDIA_CAPABILITY_TYPE_FLASH", "RICH_MEDIA_CAPABILITY_TYPE_HTML5", "SKIPPABLE_INSTREAM_VIDEO", "RICH_MEDIA_CAPABILITY_TYPE_SSL", "RICH_MEDIA_CAPABILITY_TYPE_NON_SSL", "RICH_MEDIA_CAPABILITY_TYPE_INTERSTITIAL", "NON_SKIPPABLE_INSTREAM_VIDEO", "NATIVE_ELIGIBILITY_ELIGIBLE", "NON_VPAID", "NATIVE_ELIGIBILITY_NOT_ELIGIBLE", "ANY_INTERSTITIAL", "NON_INTERSTITIAL", "IN_BANNER_VIDEO", "RENDERING_SIZELESS_ADX", "OMSDK_1_0", "RENDERING_PLAYABLE"], "enumDescriptions": ["Do not use. This is a placeholder value only.", "The creative is of type image/rich media. For pretargeting.", "The creative is of video type Adobe Flash FLV. For pretargeting.", "The creative is tagged.", "The creative is cookie targeted.", "The creative is user interest targeted.", "The creative does not expand.", "The creative expands up.", "The creative expands down.", "The creative expands left.", "The creative expands right.", "The creative expands up and left.", "The creative expands up and right.", "The creative expands down and left.", "The creative expands down and right.", "The creative type is HTML.", "The creative type is VAST video.", "The creative expands up or down.", "The creative expands left or right.", "The creative expands on any diagonal.", "The creative expands when rolled over.", "The instream vast video type is vpaid flash.", "The creative is MRAID.", "The creative is <PERSON>.", "The creative is HTML5.", "The creative has an instream VAST video type of skippable instream video. For pretargeting.", "The creative is SSL.", "The creative is non-SSL.", "The creative is an interstitial.", "The creative has an instream VAST video type of non-skippable instream video. For pretargeting.", "The creative is eligible for native.", "The creative has an instream VAST video type of non-VPAID. For pretargeting.", "The creative is not eligible for native.", "The creative has an interstitial size of any interstitial. For pretargeting.", "The creative has an interstitial size of non interstitial. For pretargeting.", "The video type is in-banner video.", "The creative can dynamically resize to fill a variety of slot sizes.", "The open measurement SDK is supported.", "The creative is considered a playable display creative."], "type": "string"}, "type": "array"}, "clickThroughUrls": {"description": "The set of destination URLs for the creative.", "items": {"type": "string"}, "type": "array"}, "corrections": {"deprecated": true, "description": "Output only. Shows any corrections that were applied to this creative.", "items": {"$ref": "Correction"}, "type": "array"}, "creativeId": {"description": "The buyer-defined creative ID of this creative. Can be used to filter the response of the creatives.list method.", "type": "string"}, "dealsStatus": {"description": "Output only. The top-level deals status of this creative. If disapproved, an entry for 'auctionType=DIRECT_DEALS' (or 'ALL') in serving_restrictions will also exist. Note that this may be nuanced with other contextual restrictions, in which case, it may be preferable to read from serving_restrictions directly. Can be used to filter the response of the creatives.list method.", "enum": ["STATUS_UNSPECIFIED", "NOT_CHECKED", "CONDITIONALLY_APPROVED", "APPROVED", "DISAPPROVED", "PENDING_REVIEW", "STATUS_TYPE_UNSPECIFIED"], "enumDescriptions": ["The status is unknown.", "The creative has not been checked.", "The creative has been conditionally approved. See serving_restrictions for details.", "The creative has been approved.", "The creative has been disapproved.", "Placeholder for transition to v1beta1. Currently not used.", "Placeholder for transition to v1beta1. Currently not used."], "type": "string"}, "declaredClickThroughUrls": {"description": "The set of declared destination URLs for the creative.", "items": {"type": "string"}, "type": "array"}, "detectedAdvertiserIds": {"description": "Output only. Detected advertiser IDs, if any.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "detectedDomains": {"description": "Output only. The detected domains for this creative.", "items": {"type": "string"}, "type": "array"}, "detectedLanguages": {"description": "Output only. The detected languages for this creative. The order is arbitrary. The codes are 2 or 5 characters and are documented at https://developers.google.com/adwords/api/docs/appendix/languagecodes.", "items": {"type": "string"}, "type": "array"}, "detectedProductCategories": {"description": "Output only. Detected product categories, if any. See the ad-product-categories.txt file in the technical documentation for a list of IDs.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "detectedSensitiveCategories": {"description": "Output only. Detected sensitive categories, if any. See the ad-sensitive-categories.txt file in the technical documentation for a list of IDs. You should use these IDs along with the excluded-sensitive-category field in the bid request to filter your bids.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "html": {"$ref": "HtmlContent", "description": "An HTML creative."}, "impressionTrackingUrls": {"description": "The set of URLs to be called to record an impression.", "items": {"type": "string"}, "type": "array"}, "native": {"$ref": "NativeContent", "description": "A native creative."}, "openAuctionStatus": {"description": "Output only. The top-level open auction status of this creative. If disapproved, an entry for 'auctionType = OPEN_AUCTION' (or 'ALL') in serving_restrictions will also exist. Note that this may be nuanced with other contextual restrictions, in which case, it may be preferable to read from serving_restrictions directly. Can be used to filter the response of the creatives.list method.", "enum": ["STATUS_UNSPECIFIED", "NOT_CHECKED", "CONDITIONALLY_APPROVED", "APPROVED", "DISAPPROVED", "PENDING_REVIEW", "STATUS_TYPE_UNSPECIFIED"], "enumDescriptions": ["The status is unknown.", "The creative has not been checked.", "The creative has been conditionally approved. See serving_restrictions for details.", "The creative has been approved.", "The creative has been disapproved.", "Placeholder for transition to v1beta1. Currently not used.", "Placeholder for transition to v1beta1. Currently not used."], "type": "string"}, "restrictedCategories": {"description": "All restricted categories for the ads that may be shown from this creative.", "items": {"enum": ["NO_RESTRICTED_CATEGORIES", "ALCOHOL"], "enumDescriptions": ["The ad has no restricted categories", "The alcohol restricted category."], "type": "string"}, "type": "array"}, "servingRestrictions": {"description": "Output only. The granular status of this ad in specific contexts. A context here relates to where something ultimately serves (for example, a physical location, a platform, an HTTPS versus HTTP request, or the type of auction).", "items": {"$ref": "ServingRestriction"}, "type": "array"}, "vendorIds": {"description": "All vendor IDs for the ads that may be shown from this creative. See https://storage.googleapis.com/adx-rtb-dictionaries/vendors.txt for possible values.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "version": {"description": "Output only. The version of this creative.", "format": "int32", "type": "integer"}, "video": {"$ref": "VideoContent", "description": "A video creative."}}, "type": "object"}, "CreativeDealAssociation": {"description": "The association between a creative and a deal.", "id": "CreativeDealAssociation", "properties": {"accountId": {"description": "The account the creative belongs to.", "type": "string"}, "creativeId": {"description": "The ID of the creative associated with the deal.", "type": "string"}, "dealsId": {"description": "The externalDealId for the deal associated with the creative.", "type": "string"}}, "type": "object"}, "CreativeRestrictions": {"description": "Represents creative restrictions associated to Programmatic Guaranteed/ Preferred Deal in Ad Manager. This doesn't apply to Private Auction and AdX Preferred Deals.", "id": "CreativeRestrictions", "properties": {"creativeFormat": {"description": "The format of the environment that the creatives will be displayed in.", "enum": ["CREATIVE_FORMAT_UNSPECIFIED", "DISPLAY", "VIDEO"], "enumDescriptions": ["A placeholder for an undefined creative format.", "A creative that will be displayed in environments such as a browser.", "A video creative that will be displayed in environments such as a video player."], "type": "string"}, "creativeSpecifications": {"items": {"$ref": "CreativeSpecification"}, "type": "array"}, "skippableAdType": {"description": "Skippable video ads allow viewers to skip ads after 5 seconds.", "enum": ["SKIPPABLE_AD_TYPE_UNSPECIFIED", "SKIPPABLE", "INSTREAM_SELECT", "NOT_SKIPPABLE"], "enumDescriptions": ["A placeholder for an undefined skippable ad type.", "This video ad can be skipped after 5 seconds.", "This video ad can be skipped after 5 seconds, and is counted as engaged view after 30 seconds. The creative is hosted on YouTube only, and viewcount of the YouTube video increments after the engaged view.", "This video ad is not skippable."], "type": "string"}}, "type": "object"}, "CreativeSize": {"description": "Specifies the size of the creative.", "id": "CreativeSize", "properties": {"allowedFormats": {"description": "What formats are allowed by the publisher. If this repeated field is empty then all formats are allowed. For example, if this field contains AllowedFormatType.AUDIO then the publisher only allows an audio ad (without any video).", "items": {"enum": ["UNKNOWN", "AUDIO"], "enumDescriptions": ["A placeholder for an undefined allowed format.", "An audio-only ad (without any video)."], "type": "string"}, "type": "array"}, "companionSizes": {"description": "For video creatives specifies the sizes of companion ads (if present). Companion sizes may be filled in only when creative_size_type = VIDEO", "items": {"$ref": "Size"}, "type": "array"}, "creativeSizeType": {"description": "The creative size type.", "enum": ["CREATIVE_SIZE_TYPE_UNSPECIFIED", "REGULAR", "INTERSTITIAL", "VIDEO", "NATIVE"], "enumDescriptions": ["A placeholder for an undefined creative size type.", "The creative is a regular desktop creative.", "The creative is an interstitial creative.", "The creative is a video creative.", "The creative is a native (mobile) creative."], "type": "string"}, "nativeTemplate": {"description": "Output only. The native template for this creative. It will have a value only if creative_size_type = CreativeSizeType.NATIVE.", "enum": ["UNKNOWN_NATIVE_TEMPLATE", "NATIVE_CONTENT_AD", "NATIVE_APP_INSTALL_AD", "NATIVE_VIDEO_CONTENT_AD", "NATIVE_VIDEO_APP_INSTALL_AD"], "enumDescriptions": ["A placeholder for an undefined native template.", "The creative is linked to native content ad.", "The creative is linked to native app install ad.", "The creative is linked to native video content ad.", "The creative is linked to native video app install ad."], "type": "string"}, "size": {"$ref": "Size", "description": "For regular or video creative size type, specifies the size of the creative"}, "skippableAdType": {"description": "The type of skippable ad for this creative. It will have a value only if creative_size_type = CreativeSizeType.VIDEO.", "enum": ["SKIPPABLE_AD_TYPE_UNSPECIFIED", "GENERIC", "INSTREAM_SELECT", "NOT_SKIPPABLE"], "enumDescriptions": ["A placeholder for an undefined skippable ad type.", "This video ad can be skipped after 5 seconds.", "This video ad can be skipped after 5 seconds, and count as engaged view after 30 seconds. The creative is hosted on YouTube only, and viewcount of the YouTube video increments after the engaged view.", "This video ad is not skippable."], "type": "string"}}, "type": "object"}, "CreativeSpecification": {"description": "Represents information for a creative that is associated with a Programmatic Guaranteed/Preferred Deal in Ad Manager.", "id": "CreativeSpecification", "properties": {"creativeCompanionSizes": {"description": "Companion sizes may be filled in only when this is a video creative.", "items": {"$ref": "AdSize"}, "type": "array"}, "creativeSize": {"$ref": "AdSize", "description": "The size of the creative."}}, "type": "object"}, "CreativeStatusRow": {"description": "The number of bids with the specified dimension values that did not win the auction (either were filtered pre-auction or lost the auction), as described by the specified creative status.", "id": "CreativeStatusRow", "properties": {"bidCount": {"$ref": "MetricValue", "description": "The number of bids with the specified status."}, "creativeStatusId": {"description": "The ID of the creative status. See [creative-status-codes](https://developers.google.com/authorized-buyers/rtb/downloads/creative-status-codes).", "format": "int32", "type": "integer"}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}}, "type": "object"}, "CriteriaTargeting": {"description": "Generic targeting used for targeting dimensions that contains a list of included and excluded numeric IDs.", "id": "CriteriaTargeting", "properties": {"excludedCriteriaIds": {"description": "A list of numeric IDs to be excluded.", "items": {"format": "int64", "type": "string"}, "type": "array"}, "targetedCriteriaIds": {"description": "A list of numeric IDs to be included.", "items": {"format": "int64", "type": "string"}, "type": "array"}}, "type": "object"}, "Date": {"description": "Represents a whole or partial calendar date, such as a birthday. The time of day and time zone are either specified elsewhere or are insignificant. The date is relative to the Gregorian Calendar. This can represent one of the following: * A full date, with non-zero year, month, and day values. * A month and day, with a zero year (for example, an anniversary). * A year on its own, with a zero month and a zero day. * A year and month, with a zero day (for example, a credit card expiration date). Related types: * google.type.TimeOfDay * google.type.DateTime * google.protobuf.Timestamp", "id": "Date", "properties": {"day": {"description": "Day of a month. Must be from 1 to 31 and valid for the year and month, or 0 to specify a year by itself or a year and month where the day isn't significant.", "format": "int32", "type": "integer"}, "month": {"description": "Month of a year. Must be from 1 to 12, or 0 to specify a year without a month and day.", "format": "int32", "type": "integer"}, "year": {"description": "Year of the date. Must be from 1 to 9999, or 0 to specify a date without a year.", "format": "int32", "type": "integer"}}, "type": "object"}, "DayPart": {"description": "Daypart targeting message that specifies if the ad can be shown only during certain parts of a day/week.", "id": "<PERSON><PERSON><PERSON>", "properties": {"dayOfWeek": {"description": "The day of the week to target. If unspecified, applicable to all days.", "enum": ["DAY_OF_WEEK_UNSPECIFIED", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["A placeholder for when the day of the week is not specified.", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"], "type": "string"}, "endTime": {"$ref": "TimeOfDay", "description": "The ending time of the day for the ad to show (minute level granularity). The end time is exclusive. This field is not available for filtering in PQL queries."}, "startTime": {"$ref": "TimeOfDay", "description": "The starting time of day for the ad to show (minute level granularity). The start time is inclusive. This field is not available for filtering in PQL queries."}}, "type": "object"}, "DayPartTargeting": {"description": "Specifies the day part targeting criteria.", "id": "DayPartTargeting", "properties": {"dayParts": {"description": "A list of day part targeting criterion.", "items": {"$ref": "<PERSON><PERSON><PERSON>"}, "type": "array"}, "timeZoneType": {"description": "The timezone to use for interpreting the day part targeting.", "enum": ["TIME_ZONE_SOURCE_UNSPECIFIED", "PUBLISHER", "USER"], "enumDescriptions": ["A placeholder for an undefined time zone source.", "Use publisher's time zone setting.", "Use the user's time zone setting."], "type": "string"}}, "type": "object"}, "Deal": {"description": "A deal represents a segment of inventory for displaying ads on. A proposal can contain multiple deals. A deal contains the terms and targeting information that is used for serving.", "id": "Deal", "properties": {"availableEndTime": {"description": "Proposed flight end time of the deal. This will generally be stored in a granularity of a second. A value is not required for Private Auction deals or Preferred Deals.", "format": "google-datetime", "type": "string"}, "availableStartTime": {"description": "Optional. Proposed flight start time of the deal. This will generally be stored in the granularity of one second since deal serving starts at seconds boundary. Any time specified with more granularity (for example, in milliseconds) will be truncated towards the start of time in seconds.", "format": "google-datetime", "type": "string"}, "buyerPrivateData": {"$ref": "PrivateData", "description": "Buyer private data (hidden from seller)."}, "createProductId": {"description": "The product ID from which this deal was created. Note: This field may be set only when creating the resource. Modifying this field while updating the resource will result in an error.", "type": "string"}, "createProductRevision": {"description": "Optional. Revision number of the product that the deal was created from. If present on create, and the server `product_revision` has advanced since the passed-in `create_product_revision`, an `ABORTED` error will be returned. Note: This field may be set only when creating the resource. Modifying this field while updating the resource will result in an error.", "format": "int64", "type": "string"}, "createTime": {"description": "Output only. The time of the deal creation.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creativePreApprovalPolicy": {"description": "Output only. Specifies the creative pre-approval policy.", "enum": ["CREATIVE_PRE_APPROVAL_POLICY_UNSPECIFIED", "SELLER_PRE_APPROVAL_REQUIRED", "SELLER_PRE_APPROVAL_NOT_REQUIRED"], "enumDescriptions": ["A placeholder for an undefined creative pre-approval policy.", "The seller needs to approve each creative before it can serve.", "The seller does not need to approve each creative before it can serve."], "readOnly": true, "type": "string"}, "creativeRestrictions": {"$ref": "CreativeRestrictions", "description": "Output only. Restricitions about the creatives associated with the deal (for example, size) This is available for Programmatic Guaranteed/Preferred Deals in Ad Manager.", "readOnly": true}, "creativeSafeFrameCompatibility": {"description": "Output only. Specifies whether the creative is safeFrame compatible.", "enum": ["CREATIVE_SAFE_FRAME_COMPATIBILITY_UNSPECIFIED", "COMPATIBLE", "INCOMPATIBLE"], "enumDescriptions": ["A placeholder for an undefined creative safe-frame compatibility.", "The creatives need to be compatible with the safe frame option.", "The creatives can be incompatible with the safe frame option."], "readOnly": true, "type": "string"}, "dealId": {"description": "Output only. A unique deal ID for the deal (server-assigned).", "readOnly": true, "type": "string"}, "dealServingMetadata": {"$ref": "DealServingMetadata", "description": "Output only. Metadata about the serving status of this deal.", "readOnly": true}, "dealTerms": {"$ref": "DealTerms", "description": "The negotiable terms of the deal."}, "deliveryControl": {"$ref": "DeliveryControl", "description": "The set of fields around delivery control that are interesting for a buyer to see but are non-negotiable. These are set by the publisher."}, "description": {"description": "Description for the deal terms.", "type": "string"}, "displayName": {"description": "The name of the deal.", "type": "string"}, "externalDealId": {"description": "Output only. The external deal ID assigned to this deal once the deal is finalized. This is the deal ID that shows up in serving/reporting etc.", "readOnly": true, "type": "string"}, "isSetupComplete": {"description": "Output only. True, if the buyside inventory setup is complete for this deal.", "readOnly": true, "type": "boolean"}, "programmaticCreativeSource": {"description": "Output only. Specifies the creative source for programmatic deals. PUBLISHER means creative is provided by seller and ADVERTISER means creative is provided by buyer.", "enum": ["PROGRAMMATIC_CREATIVE_SOURCE_UNSPECIFIED", "ADVERTISER", "PUBLISHER"], "enumDescriptions": ["A placeholder for an undefined programmatic creative source.", "The advertiser provides the creatives.", "The publisher provides the creatives to be served."], "readOnly": true, "type": "string"}, "proposalId": {"description": "Output only. ID of the proposal that this deal is part of.", "readOnly": true, "type": "string"}, "sellerContacts": {"description": "Output only. <PERSON><PERSON> contact information for the deal.", "items": {"$ref": "ContactInformation"}, "readOnly": true, "type": "array"}, "syndicationProduct": {"description": "The syndication product associated with the deal. Note: This field may be set only when creating the resource. Modifying this field while updating the resource will result in an error.", "enum": ["SYNDICATION_PRODUCT_UNSPECIFIED", "CONTENT", "MOBILE", "VIDEO", "GAMES"], "enumDescriptions": ["A placeholder for an undefined syndication product.", "This typically represents a web page.", "This represents a mobile property.", "This represents video ad formats.", "This represents ads shown within games."], "type": "string"}, "targeting": {"$ref": "MarketplaceTargeting", "description": "Output only. Specifies the subset of inventory targeted by the deal.", "readOnly": true}, "targetingCriterion": {"description": "The shared targeting visible to buyers and sellers. Each shared targeting entity is AND'd together.", "items": {"$ref": "TargetingCriteria"}, "type": "array"}, "updateTime": {"description": "Output only. The time when the deal was last updated.", "format": "google-datetime", "readOnly": true, "type": "string"}, "webPropertyCode": {"description": "The web property code for the seller copied over from the product.", "type": "string"}}, "type": "object"}, "DealPauseStatus": {"description": "Tracks which parties (if any) have paused a deal. The deal is considered paused if either hasBuyerPaused or hasSellPaused is true.", "id": "DealPauseStatus", "properties": {"buyerPauseReason": {"description": "The buyer's reason for pausing, if the buyer paused the deal.", "type": "string"}, "firstPausedBy": {"description": "The role of the person who first paused this deal.", "enum": ["BUYER_SELLER_ROLE_UNSPECIFIED", "BUYER", "SELLER"], "enumDescriptions": ["A placeholder for an undefined buyer/seller role.", "Specifies the role as buyer.", "Specifies the role as seller."], "type": "string"}, "hasBuyerPaused": {"description": "True, if the buyer has paused the deal unilaterally.", "type": "boolean"}, "hasSellerPaused": {"description": "True, if the seller has paused the deal unilaterally.", "type": "boolean"}, "sellerPauseReason": {"description": "The seller's reason for pausing, if the seller paused the deal.", "type": "string"}}, "type": "object"}, "DealServingMetadata": {"description": "Message captures metadata about the serving status of a deal.", "id": "DealServingMetadata", "properties": {"dealPauseStatus": {"$ref": "DealPauseStatus", "description": "Output only. Tracks which parties (if any) have paused a deal.", "readOnly": true}}, "type": "object"}, "DealTerms": {"description": "The deal terms specify the details of a Product/deal. They specify things like price per buyer, the type of pricing model (for example, fixed price, auction) and expected impressions from the publisher.", "id": "DealTerms", "properties": {"brandingType": {"description": "Visibility of the URL in bid requests. (default: BRANDED)", "enum": ["BRANDING_TYPE_UNSPECIFIED", "BRANDED", "SEMI_TRANSPARENT"], "enumDescriptions": ["A placeholder for an undefined branding type.", "Full URL is included in bid requests.", "A TopLevelDomain or masked URL is sent in bid requests rather than the full one."], "type": "string"}, "description": {"description": "Publisher provided description for the terms.", "type": "string"}, "estimatedGrossSpend": {"$ref": "Price", "description": "Non-binding estimate of the estimated gross spend for this deal. Can be set by buyer or seller."}, "estimatedImpressionsPerDay": {"description": "Non-binding estimate of the impressions served per day. Can be set by buyer or seller.", "format": "int64", "type": "string"}, "guaranteedFixedPriceTerms": {"$ref": "GuaranteedFixedPriceTerms", "description": "The terms for guaranteed fixed price deals."}, "nonGuaranteedAuctionTerms": {"$ref": "NonGuaranteedAuctionTerms", "description": "The terms for non-guaranteed auction deals."}, "nonGuaranteedFixedPriceTerms": {"$ref": "NonGuaranteedFixedPriceTerms", "description": "The terms for non-guaranteed fixed price deals."}, "sellerTimeZone": {"description": "The time zone name. For deals with Cost Per Day billing, defines the time zone used to mark the boundaries of a day. It should be an IANA TZ name, such as \"America/Los_Angeles\". For more information, see https://en.wikipedia.org/wiki/List_of_tz_database_time_zones.", "type": "string"}}, "type": "object"}, "DeliveryControl": {"description": "Message contains details about how the deals will be paced.", "id": "DeliveryControl", "properties": {"creativeBlockingLevel": {"description": "Output only. Specified the creative blocking levels to be applied.", "enum": ["CREATIVE_BLOCKING_LEVEL_UNSPECIFIED", "PUBLISHER_BLOCKING_RULES", "ADX_POLICY_BLOCKING_ONLY"], "enumDescriptions": ["A placeholder for an undefined creative blocking level.", "Publisher blocking rules will be applied.", "The Ad Exchange policy blocking rules will be applied."], "readOnly": true, "type": "string"}, "deliveryRateType": {"description": "Output only. Specifies how the impression delivery will be paced.", "enum": ["DELIVERY_RATE_TYPE_UNSPECIFIED", "EVENLY", "FRONT_LOADED", "AS_FAST_AS_POSSIBLE"], "enumDescriptions": ["A placeholder for an undefined delivery rate type.", "Impressions are served uniformly over the life of the deal.", "Impressions are served front-loaded.", "Impressions are served as fast as possible."], "readOnly": true, "type": "string"}, "frequencyCaps": {"description": "Output only. Specifies any frequency caps.", "items": {"$ref": "FrequencyCap"}, "readOnly": true, "type": "array"}}, "type": "object"}, "Disapproval": {"description": "Output only. The reason and details for a disapproval.", "id": "Disapproval", "properties": {"details": {"description": "Additional details about the reason for disapproval.", "items": {"type": "string"}, "type": "array"}, "reason": {"description": "The categorized reason for disapproval.", "enum": ["LENGTH_OF_IMAGE_ANIMATION", "BROKEN_URL", "MEDIA_NOT_FUNCTIONAL", "INVALID_FOURTH_PARTY_CALL", "INCORRECT_REMARKETING_DECLARATION", "LANDING_PAGE_ERROR", "AD_SIZE_DOES_NOT_MATCH_AD_SLOT", "NO_BORDER", "FOURTH_PARTY_BROWSER_COOKIES", "LSO_OBJECTS", "BLANK_CREATIVE", "DESTINATION_URLS_UNDECLARED", "PROBLEM_WITH_CLICK_MACRO", "INCORRECT_AD_TECHNOLOGY_DECLARATION", "INCORRECT_DESTINATION_URL_DECLARATION", "EXPANDABLE_INCORRECT_DIRECTION", "EXPANDABLE_DIRECTION_NOT_SUPPORTED", "EXPANDABLE_INVALID_VENDOR", "EXPANDABLE_FUNCTIONALITY", "VIDEO_INVALID_VENDOR", "VIDEO_UNSUPPORTED_LENGTH", "VIDEO_UNSUPPORTED_FORMAT", "VIDEO_FUNCTIONALITY", "LANDING_PAGE_DISABLED", "MALWARE_SUSPECTED", "ADULT_IMAGE_OR_VIDEO", "INACCURATE_AD_TEXT", "COUNTERFEIT_DESIGNER_GOODS", "POP_UP", "INVALID_RTB_PROTOCOL_USAGE", "RAW_IP_ADDRESS_IN_SNIPPET", "UNACCEPTABLE_CONTENT_SOFTWARE", "UNAUTHORIZED_COOKIE_ON_GOOGLE_DOMAIN", "UNDECLARED_FLASH_OBJECTS", "INVALID_SSL_DECLARATION", "DIRECT_DOWNLOAD_IN_AD", "MAXIMUM_DOWNLOAD_SIZE_EXCEEDED", "DESTINATION_URL_SITE_NOT_CRAWLABLE", "BAD_URL_LEGAL_DISAPPROVAL", "PHARMA_GAMBLING_ALCOHOL_NOT_ALLOWED", "DYNAMIC_DNS_AT_DESTINATION_URL", "POOR_IMAGE_OR_VIDEO_QUALITY", "UNACCEPTABLE_IMAGE_CONTENT", "INCORRECT_IMAGE_LAYOUT", "IRRELEVANT_IMAGE_OR_VIDEO", "DESTINATION_SITE_DOES_NOT_ALLOW_GOING_BACK", "MISLEADING_CLAIMS_IN_AD", "RESTRICTED_PRODUCTS", "UNACCEPTABLE_CONTENT", "AUTOMATED_AD_CLICKING", "INVALID_URL_PROTOCOL", "UNDECLARED_RESTRICTED_CONTENT", "INVALID_REMARKETING_LIST_USAGE", "DESTINATION_SITE_NOT_CRAWLABLE_ROBOTS_TXT", "CLICK_TO_DOWNLOAD_NOT_AN_APP", "INACCURATE_REVIEW_EXTENSION", "SEXUALLY_EXPLICIT_CONTENT", "GAINING_AN_UNFAIR_ADVANTAGE", "GAMING_THE_GOOGLE_NETWORK", "DANGEROUS_PRODUCTS_KNIVES", "DANGEROUS_PRODUCTS_EXPLOSIVES", "DANGEROUS_PRODUCTS_GUNS", "DANGEROUS_PRODUCTS_DRUGS", "DANGEROUS_PRODUCTS_TOBACCO", "DANGEROUS_PRODUCTS_WEAPONS", "UNCLEAR_OR_IRRELEVANT_AD", "PROFESSIONAL_STANDARDS", "DYSFUNCTIONAL_PROMOTION", "INVALID_INTEREST_BASED_AD", "MISUSE_OF_PERSONAL_INFORMATION", "OMISSION_OF_RELEVANT_INFORMATION", "UNAVAILABLE_PROMOTIONS", "MISLEADING_PROMOTIONS", "INAPPROPRIATE_CONTENT", "SENSITIVE_EVENTS", "SHOCKING_CONTENT", "ENABLING_DISHONEST_BEHAVIOR", "TECHNICAL_REQUIREMENTS", "RESTRICTED_POLITICAL_CONTENT", "UNSUPPORTED_CONTENT", "INVALID_BIDDING_METHOD", "VIDEO_TOO_LONG", "VIOLATES_JAPANESE_PHARMACY_LAW", "UNACCREDITED_PET_PHARMACY", "ABORTION", "CONTRACEPTIVES", "NEED_CERTIFICATES_TO_ADVERTISE_IN_CHINA", "KCDSP_REGISTRATION", "NOT_FAMILY_SAFE", "CLINICAL_TRIAL_RECRUITMENT", "MAXIMUM_NUMBER_OF_HTTP_CALLS_EXCEEDED", "MAXIMUM_NUMBER_OF_COOKIES_EXCEEDED", "PERSONAL_LOANS", "UNSUPPORTED_FLASH_CONTENT", "MISUSE_BY_OMID_SCRIPT", "NON_WHITELISTED_OMID_VENDOR", "DESTINATION_EXPERIENCE", "UNSUPPORTED_LANGUAGE", "NON_SSL_COMPLIANT", "TEMPORARY_PAUSE", "BAIL_BONDS", "EXPERIMENTAL_MEDICAL_TREATMENT"], "enumDescriptions": ["The length of the image animation is longer than allowed.", "The click through URL doesn't work properly.", "Something is wrong with the creative itself.", "The ad makes a fourth party call to an unapproved vendor.", "The ad targets consumers using remarketing lists and/or collects data for subsequent use in retargeting, but does not correctly declare that use.", "Clicking on the ad leads to an error page.", "The ad size when rendered does not match the declaration.", "Ads with a white background require a border, which was missing.", "The creative attempts to set cookies from a fourth party that is not certified.", "The creative sets an LSO object.", "The ad serves a blank.", "The ad uses rotation, but not all destination URLs were declared.", "There is a problem with the way the click macro is used.", "The ad technology declaration is not accurate.", "The actual destination URL does not match the declared destination URL.", "The declared expanding direction does not match the actual direction.", "The ad does not expand in a supported direction.", "The ad uses an expandable vendor that is not supported.", "There was an issue with the expandable ad.", "The ad uses a video vendor that is not supported.", "The length of the video ad is not supported.", "The format of the video ad is not supported.", "There was an issue with the video ad.", "The landing page does not conform to Ad Exchange policy.", "The ad or the landing page may contain malware.", "The ad contains adult images or video content.", "The ad contains text that is unclear or inaccurate.", "The ad promotes counterfeit designer goods.", "The ad causes a popup window to appear.", "The creative does not follow policies set for the RTB protocol.", "The ad contains a URL that uses a numeric IP address for the domain.", "The ad or landing page contains unacceptable content because it initiated a software or executable download.", "The ad set an unauthorized cookie on a Google domain.", "Flash content found when no flash was declared.", "SSL support declared but not working correctly.", "Rich Media - Direct Download in Ad (ex. PDF download).", "Maximum download size exceeded.", "Bad Destination URL: Site Not Crawlable.", "Bad URL: Legal disapproval.", "Pharmaceuticals, Gambling, Alcohol not allowed and at least one was detected.", "Dynamic DNS at Destination URL.", "Poor Image / Video Quality.", "For example, Image Trick to Click.", "Incorrect Image Layout.", "Irrelevant Image / Video.", "Broken back button.", "Misleading/Inaccurate claims in ads.", "Restricted Products.", "Unacceptable content. For example, malware.", "The ad automatically redirects to the destination site without a click, or reports a click when none were made.", "The ad uses URL protocols that do not exist or are not allowed on AdX.", "Restricted content (for example, alcohol) was found in the ad but not declared.", "Violation of the remarketing list policy.", "The destination site's robot.txt file prevents it from being crawled.", "Click to download must link to an app.", "A review extension must be an accurate review.", "Sexually explicit content.", "The ad tries to gain an unfair traffic advantage.", "The ad tries to circumvent Google's advertising systems.", "The ad promotes dangerous knives.", "The ad promotes explosives.", "The ad promotes guns & parts.", "The ad promotes recreational drugs/services & related equipment.", "The ad promotes tobacco products/services & related equipment.", "The ad promotes weapons.", "The ad is unclear or irrelevant to the destination site.", "The ad does not meet professional standards.", "The promotion is unnecessarily difficult to navigate.", "Violation of Google's policy for interest-based ads.", "Misuse of personal information.", "Omission of relevant information.", "Unavailable promotions.", "Misleading or unrealistic promotions.", "Offensive or inappropriate content.", "Capitalizing on sensitive events.", "Shocking content.", "Products & Services that enable dishonest behavior.", "The ad does not meet technical requirements.", "Restricted political content.", "Unsupported content.", "Invalid bidding method.", "Video length exceeds limits.", "Unacceptable content: Japanese healthcare.", "Online pharmacy ID required.", "Unacceptable content: Abortion.", "Unacceptable content: Birth control.", "Restricted in China.", "Unacceptable content: Korean healthcare.", "Non-family safe or adult content.", "Clinical trial recruitment.", "Maximum number of HTTP calls exceeded.", "Maximum number of cookies exceeded.", "Financial service ad does not adhere to specifications.", "Flash content was found in an unsupported context.", "Misuse by an Open Measurement SDK script.", "Use of an Open Measurement SDK vendor not on approved vendor list.", "Unacceptable landing page.", "Unsupported language.", "Non-SSL compliant.", "Temporary pausing of creative.", "Promotes services related to bail bonds.", "Promotes speculative and/or experimental medical treatments."], "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "FilterSet": {"description": "A set of filters that is applied to a request for data. Within a filter set, an AND operation is performed across the filters represented by each field. An OR operation is performed across the filters represented by the multiple values of a repeated field, for example, \"format=VIDEO AND deal_id=12 AND (seller_network_id=34 OR seller_network_id=56)\".", "id": "FilterSet", "properties": {"absoluteDateRange": {"$ref": "AbsoluteDateRange", "description": "An absolute date range, defined by a start date and an end date. Interpreted relative to Pacific time zone."}, "breakdownDimensions": {"description": "The set of dimensions along which to break down the response; may be empty. If multiple dimensions are requested, the breakdown is along the Cartesian product of the requested dimensions.", "items": {"enum": ["BREAKDOWN_DIMENSION_UNSPECIFIED", "PUBLISHER_IDENTIFIER"], "enumDescriptions": ["A placeholder for an unspecified dimension; should not be used.", "The response should be broken down by publisher identifier. This option is available only for Open Bidding buyers."], "type": "string"}, "type": "array"}, "creativeId": {"description": "The ID of the creative on which to filter; optional. This field may be set only for a filter set that accesses account-level troubleshooting data, for example, one whose name matches the `bidders/*/accounts/*/filterSets/*` pattern.", "type": "string"}, "dealId": {"description": "The ID of the deal on which to filter; optional. This field may be set only for a filter set that accesses account-level troubleshooting data, for example, one whose name matches the `bidders/*/accounts/*/filterSets/*` pattern.", "format": "int64", "type": "string"}, "environment": {"description": "The environment on which to filter; optional.", "enum": ["ENVIRONMENT_UNSPECIFIED", "WEB", "APP"], "enumDescriptions": ["A placeholder for an undefined environment; indicates that no environment filter will be applied.", "The ad impression appears on the web.", "The ad impression appears in an app."], "type": "string"}, "format": {"description": "Creative format bidded on or allowed to bid on, can be empty.", "enum": ["FORMAT_UNSPECIFIED", "NATIVE_DISPLAY", "NATIVE_VIDEO", "NON_NATIVE_DISPLAY", "NON_NATIVE_VIDEO"], "enumDescriptions": ["A placeholder for an undefined format; indicates that no format filter will be applied.", "The ad impression is a native ad, and display (for example, image) format.", "The ad impression is a native ad, and video format.", "The ad impression is not a native ad, and display (for example, image) format.", "The ad impression is not a native ad, and video format."], "type": "string"}, "formats": {"deprecated": true, "description": "Creative formats bidded on or allowed to bid on, can be empty. Although this field is a list, it can only be populated with a single item. A HTTP 400 bad request error will be returned in the response if you specify multiple items.", "items": {"enum": ["FORMAT_UNSPECIFIED", "NATIVE_DISPLAY", "NATIVE_VIDEO", "NON_NATIVE_DISPLAY", "NON_NATIVE_VIDEO"], "enumDescriptions": ["A placeholder for an undefined format; indicates that no format filter will be applied.", "The ad impression is a native ad, and display (for example, image) format.", "The ad impression is a native ad, and video format.", "The ad impression is not a native ad, and display (for example, image) format.", "The ad impression is not a native ad, and video format."], "type": "string"}, "type": "array"}, "name": {"description": "A user-defined name of the filter set. Filter set names must be unique globally and match one of the patterns: - `bidders/*/filterSets/*` (for accessing bidder-level troubleshooting data) - `bidders/*/accounts/*/filterSets/*` (for accessing account-level troubleshooting data) This field is required in create operations.", "type": "string"}, "platforms": {"description": "The list of platforms on which to filter; may be empty. The filters represented by multiple platforms are ORed together (for example, if non-empty, results must match any one of the platforms).", "items": {"enum": ["PLATFORM_UNSPECIFIED", "DESKTOP", "TABLET", "MOBILE"], "enumDescriptions": ["A placeholder for an undefined platform; indicates that no platform filter will be applied.", "The ad impression appears on a desktop.", "The ad impression appears on a tablet.", "The ad impression appears on a mobile device."], "type": "string"}, "type": "array"}, "publisherIdentifiers": {"description": "For Open Bidding partners only. The list of publisher identifiers on which to filter; may be empty. The filters represented by multiple publisher identifiers are ORed together.", "items": {"type": "string"}, "type": "array"}, "realtimeTimeRange": {"$ref": "RealtimeTimeRange", "description": "An open-ended realtime time range, defined by the aggregation start timestamp."}, "relativeDateRange": {"$ref": "RelativeDateRang<PERSON>", "description": "A relative date range, defined by an offset from today and a duration. Interpreted relative to Pacific time zone."}, "sellerNetworkIds": {"description": "For Authorized Buyers only. The list of IDs of the seller (publisher) networks on which to filter; may be empty. The filters represented by multiple seller network IDs are ORed together (for example, if non-empty, results must match any one of the publisher networks). See [seller-network-ids](https://developers.google.com/authorized-buyers/rtb/downloads/seller-network-ids) file for the set of existing seller network IDs.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "timeSeriesGranularity": {"description": "The granularity of time intervals if a time series breakdown is preferred; optional.", "enum": ["TIME_SERIES_GRANULARITY_UNSPECIFIED", "HOURLY", "DAILY"], "enumDescriptions": ["A placeholder for an unspecified interval; no time series is applied. All rows in response will contain data for the entire requested time range.", "Indicates that data will be broken down by the hour.", "Indicates that data will be broken down by the day."], "type": "string"}}, "type": "object"}, "FilteredBidCreativeRow": {"description": "The number of filtered bids with the specified dimension values that have the specified creative.", "id": "FilteredBidCreativeRow", "properties": {"bidCount": {"$ref": "MetricValue", "description": "The number of bids with the specified creative."}, "creativeId": {"description": "The ID of the creative.", "type": "string"}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}}, "type": "object"}, "FilteredBidDetailRow": {"description": "The number of filtered bids with the specified dimension values, among those filtered due to the requested filtering reason (for example, creative status), that have the specified detail.", "id": "FilteredBidDetailRow", "properties": {"bidCount": {"$ref": "MetricValue", "description": "The number of bids with the specified detail."}, "detail": {"description": "The ID of the detail, can be numeric or text. The associated value can be looked up in the dictionary file corresponding to the DetailType in the response message.", "type": "string"}, "detailId": {"deprecated": true, "description": "Note: this field will be deprecated, use \"detail\" field instead. When \"detail\" field represents an integer value, this field is populated as the same integer value \"detail\" field represents, otherwise this field will be 0. The ID of the detail. The associated value can be looked up in the dictionary file corresponding to the DetailType in the response message.", "format": "int32", "type": "integer"}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}}, "type": "object"}, "FirstPartyMobileApplicationTargeting": {"description": "Represents a list of targeted and excluded mobile application IDs that publishers own. Mobile application IDs are from App Store and Google Play Store. Android App ID, for example, com.google.android.apps.maps, can be found in Google Play Store URL. iOS App ID (which is a number) can be found at the end of iTunes store URL. First party mobile applications is either included or excluded.", "id": "FirstPartyMobileApplicationTargeting", "properties": {"excludedAppIds": {"description": "A list of application IDs to be excluded.", "items": {"type": "string"}, "type": "array"}, "targetedAppIds": {"description": "A list of application IDs to be included.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "FrequencyCap": {"description": "Frequency cap.", "id": "FrequencyCap", "properties": {"maxImpressions": {"description": "The maximum number of impressions that can be served to a user within the specified time period.", "format": "int32", "type": "integer"}, "numTimeUnits": {"description": "The amount of time, in the units specified by time_unit_type. Defines the amount of time over which impressions per user are counted and capped.", "format": "int32", "type": "integer"}, "timeUnitType": {"description": "The time unit. Along with num_time_units defines the amount of time over which impressions per user are counted and capped.", "enum": ["TIME_UNIT_TYPE_UNSPECIFIED", "MINUTE", "HOUR", "DAY", "WEEK", "MONTH", "LIFETIME", "POD", "STREAM"], "enumDescriptions": ["A placeholder for an undefined time unit type. This just indicates the variable with this value hasn't been initialized.", "Minute", "Hour", "Day", "Week", "Month", "Lifetime", "Pod", "Stream"], "type": "string"}}, "type": "object"}, "GuaranteedFixedPriceTerms": {"description": "Terms for Programmatic Guaranteed Deals.", "id": "GuaranteedFixedPriceTerms", "properties": {"fixedPrices": {"description": "Fixed price for the specified buyer.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}, "guaranteedImpressions": {"description": "Guaranteed impressions as a percentage. This is the percentage of guaranteed looks that the buyer is guaranteeing to buy.", "format": "int64", "type": "string"}, "guaranteedLooks": {"description": "Count of guaranteed looks. Required for deal, optional for product. For CPD deals, buyer changes to guaranteed_looks will be ignored.", "format": "int64", "type": "string"}, "impressionCap": {"description": "The lifetime impression cap for CPM sponsorship deals. The deal will stop serving when the cap is reached.", "format": "int64", "type": "string"}, "minimumDailyLooks": {"description": "Daily minimum looks for CPD deal types. For CPD deals, buyer should negotiate on this field instead of guaranteed_looks.", "format": "int64", "type": "string"}, "percentShareOfVoice": {"description": "For sponsorship deals, this is the percentage of the seller's eligible impressions that the deal will serve until the cap is reached.", "format": "int64", "type": "string"}, "reservationType": {"description": "The reservation type for a Programmatic Guaranteed deal. This indicates whether the number of impressions is fixed, or a percent of available impressions. If not specified, the default reservation type is STANDARD.", "enum": ["RESERVATION_TYPE_UNSPECIFIED", "STANDARD", "SPONSORSHIP"], "enumDescriptions": ["An unspecified reservation type.", "Non-sponsorship deal.", "Sponsorship deals don't have impression goal (guaranteed_looks) and they are served based on the flight dates. For CPM Sponsorship deals, impression_cap is the lifetime impression limit."], "type": "string"}}, "type": "object"}, "HtmlContent": {"description": "HTML content for a creative.", "id": "HtmlContent", "properties": {"height": {"description": "The height of the HTML snippet in pixels.", "format": "int32", "type": "integer"}, "snippet": {"description": "The HTML snippet that displays the ad when inserted in the web page.", "type": "string"}, "width": {"description": "The width of the HTML snippet in pixels.", "format": "int32", "type": "integer"}}, "type": "object"}, "Image": {"description": "An image resource. You may provide a larger image than was requested, so long as the aspect ratio is preserved.", "id": "Image", "properties": {"height": {"description": "Image height in pixels.", "format": "int32", "type": "integer"}, "url": {"description": "The URL of the image.", "type": "string"}, "width": {"description": "Image width in pixels.", "format": "int32", "type": "integer"}}, "type": "object"}, "ImpressionMetricsRow": {"description": "The set of metrics that are measured in numbers of impressions, representing how many impressions with the specified dimension values were considered eligible at each stage of the bidding funnel.", "id": "ImpressionMetricsRow", "properties": {"availableImpressions": {"$ref": "MetricValue", "description": "The number of impressions available to the buyer on Ad Exchange. In some cases this value may be unavailable."}, "bidRequests": {"$ref": "MetricValue", "description": "The number of impressions for which Ad Exchange sent the buyer a bid request."}, "inventoryMatches": {"$ref": "MetricValue", "description": "The number of impressions that match the buyer's inventory pretargeting."}, "responsesWithBids": {"$ref": "MetricValue", "description": "The number of impressions for which Ad Exchange received a response from the buyer that contained at least one applicable bid."}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}, "successfulResponses": {"$ref": "MetricValue", "description": "The number of impressions for which the buyer successfully sent a response to Ad Exchange."}}, "type": "object"}, "InventorySizeTargeting": {"description": "Represents the size of an ad unit that can be targeted on an ad request. It only applies to Private Auction, AdX Preferred Deals and Auction Packages. This targeting does not apply to Programmatic Guaranteed and Preferred Deals in Ad Manager.", "id": "InventorySizeTargeting", "properties": {"excludedInventorySizes": {"description": "A list of inventory sizes to be excluded.", "items": {"$ref": "AdSize"}, "type": "array"}, "targetedInventorySizes": {"description": "A list of inventory sizes to be included.", "items": {"$ref": "AdSize"}, "type": "array"}}, "type": "object"}, "ListBidMetricsResponse": {"description": "Response message for listing the metrics that are measured in number of bids.", "id": "ListBidMetricsResponse", "properties": {"bidMetricsRows": {"description": "List of rows, each containing a set of bid metrics.", "items": {"$ref": "BidMetricsRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListBidMetricsRequest.pageToken field in the subsequent call to the bidMetrics.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListBidResponseErrorsResponse": {"description": "Response message for listing all reasons that bid responses resulted in an error.", "id": "ListBidResponseErrorsResponse", "properties": {"calloutStatusRows": {"description": "List of rows, with counts of bid responses aggregated by callout status.", "items": {"$ref": "CalloutStatusRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListBidResponseErrorsRequest.pageToken field in the subsequent call to the bidResponseErrors.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListBidResponsesWithoutBidsResponse": {"description": "Response message for listing all reasons that bid responses were considered to have no applicable bids.", "id": "ListBidResponsesWithoutBidsResponse", "properties": {"bidResponseWithoutBidsStatusRows": {"description": "List of rows, with counts of bid responses without bids aggregated by status.", "items": {"$ref": "BidResponseWithoutBidsStatusRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListBidResponsesWithoutBidsRequest.pageToken field in the subsequent call to the bidResponsesWithoutBids.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListClientUserInvitationsResponse": {"id": "ListClientUserInvitationsResponse", "properties": {"invitations": {"description": "The returned list of client users.", "items": {"$ref": "ClientUserInvitation"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListClientUserInvitationsRequest.pageToken field in the subsequent call to the clients.invitations.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListClientUsersResponse": {"id": "ListClientUsersResponse", "properties": {"nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListClientUsersRequest.pageToken field in the subsequent call to the clients.invitations.list method to retrieve the next page of results.", "type": "string"}, "users": {"description": "The returned list of client users.", "items": {"$ref": "ClientUser"}, "type": "array"}}, "type": "object"}, "ListClientsResponse": {"id": "ListClientsResponse", "properties": {"clients": {"description": "The returned list of clients.", "items": {"$ref": "Client"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListClientsRequest.pageToken field in the subsequent call to the accounts.clients.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListCreativeStatusBreakdownByCreativeResponse": {"description": "Response message for listing all creatives associated with a given filtered bid reason.", "id": "ListCreativeStatusBreakdownByCreativeResponse", "properties": {"filteredBidCreativeRows": {"description": "List of rows, with counts of bids with a given creative status aggregated by creative.", "items": {"$ref": "FilteredBidCreativeRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListCreativeStatusBreakdownByCreativeRequest.pageToken field in the subsequent call to the filteredBids.creatives.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListCreativeStatusBreakdownByDetailResponse": {"description": "Response message for listing all details associated with a given filtered bid reason.", "id": "ListCreativeStatusBreakdownByDetailResponse", "properties": {"detailType": {"description": "The type of detail that the detail IDs represent.", "enum": ["DETAIL_TYPE_UNSPECIFIED", "CREATIVE_ATTRIBUTE", "VENDOR", "SENSITIVE_CATEGORY", "PRODUCT_CATEGORY", "DISAPPROVAL_REASON", "POLICY_TOPIC", "ATP_VENDOR", "VENDOR_DOMAIN", "GVL_ID"], "enumDescriptions": ["A placeholder for an undefined status. This value will never be returned in responses.", "Indicates that the detail ID refers to a creative attribute; see [publisher-excludable-creative-attributes](https://developers.google.com/authorized-buyers/rtb/downloads/publisher-excludable-creative-attributes).", "Indicates that the detail ID refers to a vendor; see [vendors](https://developers.google.com/authorized-buyers/rtb/downloads/vendors). This namespace is different from that of the `ATP_VENDOR` detail type.", "Indicates that the detail ID refers to a sensitive category; see [ad-sensitive-categories](https://developers.google.com/authorized-buyers/rtb/downloads/ad-sensitive-categories).", "Indicates that the detail ID refers to a product category; see [ad-product-categories](https://developers.google.com/authorized-buyers/rtb/downloads/ad-product-categories).", "Indicates that the detail ID refers to a disapproval reason; see DisapprovalReason enum in [snippet-status-report-proto](https://developers.google.com/authorized-buyers/rtb/downloads/snippet-status-report-proto).", "Indicates that the detail ID refers to a policy topic.", "Indicates that the detail ID refers to an ad technology provider (ATP); see [providers] (https://storage.googleapis.com/adx-rtb-dictionaries/providers.csv). This namespace is different from the `VENDOR` detail type; see [ad technology providers](https://support.google.com/admanager/answer/9012903) for more information.", "Indicates that the detail string refers the domain of an unknown vendor.", "Indicates that the detail ID refers an IAB GVL ID which Google did not detect in the latest TCF Vendor List. See [Global Vendor List] (https://vendor-list.consensu.org/v2/vendor-list.json)"], "type": "string"}, "filteredBidDetailRows": {"description": "List of rows, with counts of bids with a given creative status aggregated by detail.", "items": {"$ref": "FilteredBidDetailRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListCreativeStatusBreakdownByDetailRequest.pageToken field in the subsequent call to the filteredBids.details.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListCreativesResponse": {"description": "A response for listing creatives.", "id": "ListCreativesResponse", "properties": {"creatives": {"description": "The list of creatives.", "items": {"$ref": "Creative"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListCreativesRequest.page_token field in the subsequent call to `ListCreatives` method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListDealAssociationsResponse": {"description": "A response for listing creative and deal associations", "id": "ListDealAssociationsResponse", "properties": {"associations": {"description": "The list of associations.", "items": {"$ref": "CreativeDealAssociation"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListDealAssociationsRequest.page_token field in the subsequent call to 'ListDealAssociation' method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListFilterSetsResponse": {"description": "Response message for listing filter sets.", "id": "ListFilterSetsResponse", "properties": {"filterSets": {"description": "The filter sets belonging to the buyer.", "items": {"$ref": "FilterSet"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListFilterSetsRequest.pageToken field in the subsequent call to the accounts.filterSets.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListFilteredBidRequestsResponse": {"description": "Response message for listing all reasons that bid requests were filtered and not sent to the buyer.", "id": "ListFilteredBidRequestsResponse", "properties": {"calloutStatusRows": {"description": "List of rows, with counts of filtered bid requests aggregated by callout status.", "items": {"$ref": "CalloutStatusRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListFilteredBidRequestsRequest.pageToken field in the subsequent call to the filteredBidRequests.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListFilteredBidsResponse": {"description": "Response message for listing all reasons that bids were filtered from the auction.", "id": "ListFilteredBidsResponse", "properties": {"creativeStatusRows": {"description": "List of rows, with counts of filtered bids aggregated by filtering reason (for example, creative status).", "items": {"$ref": "CreativeStatusRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListFilteredBidsRequest.pageToken field in the subsequent call to the filteredBids.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListImpressionMetricsResponse": {"description": "Response message for listing the metrics that are measured in number of impressions.", "id": "ListImpressionMetricsResponse", "properties": {"impressionMetricsRows": {"description": "List of rows, each containing a set of impression metrics.", "items": {"$ref": "ImpressionMetricsRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListImpressionMetricsRequest.pageToken field in the subsequent call to the impressionMetrics.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListLosingBidsResponse": {"description": "Response message for listing all reasons that bids lost in the auction.", "id": "ListLosingBidsResponse", "properties": {"creativeStatusRows": {"description": "List of rows, with counts of losing bids aggregated by loss reason (for example, creative status).", "items": {"$ref": "CreativeStatusRow"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListLosingBidsRequest.pageToken field in the subsequent call to the losingBids.list method to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListNonBillableWinningBidsResponse": {"description": "Response message for listing all reasons for which a buyer was not billed for a winning bid.", "id": "ListNonBillableWinningBidsResponse", "properties": {"nextPageToken": {"description": "A token to retrieve the next page of results. Pass this value in the ListNonBillableWinningBidsRequest.pageToken field in the subsequent call to the nonBillableWinningBids.list method to retrieve the next page of results.", "type": "string"}, "nonBillableWinningBidStatusRows": {"description": "List of rows, with counts of bids not billed aggregated by reason.", "items": {"$ref": "NonBillableWinningBidStatusRow"}, "type": "array"}}, "type": "object"}, "ListProductsResponse": {"description": "Response message for listing products visible to the buyer.", "id": "ListProductsResponse", "properties": {"nextPageToken": {"description": "List pagination support.", "type": "string"}, "products": {"description": "The list of matching products at their head revision number.", "items": {"$ref": "Product"}, "type": "array"}}, "type": "object"}, "ListProposalsResponse": {"description": "Response message for listing proposals.", "id": "ListProposalsResponse", "properties": {"nextPageToken": {"description": "Continuation token for fetching the next page of results.", "type": "string"}, "proposals": {"description": "The list of proposals.", "items": {"$ref": "Proposal"}, "type": "array"}}, "type": "object"}, "ListPublisherProfilesResponse": {"description": "Response message for profiles visible to the buyer.", "id": "ListPublisherProfilesResponse", "properties": {"nextPageToken": {"description": "List pagination support", "type": "string"}, "publisherProfiles": {"description": "The list of matching publisher profiles.", "items": {"$ref": "PublisherProfile"}, "type": "array"}}, "type": "object"}, "LocationContext": {"description": "Output only. The Geo criteria the restriction applies to.", "id": "LocationContext", "properties": {"geoCriteriaIds": {"description": "IDs representing the geo location for this context. Refer to the [geo-table.csv](https://storage.googleapis.com/adx-rtb-dictionaries/geo-table.csv) file for different geo criteria IDs.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "MarketplaceTargeting": {"description": "Targeting represents different criteria that can be used by advertisers to target ad inventory. For example, they can choose to target ad requests only if the user is in the US. Multiple types of targeting are always applied as a logical AND, unless noted otherwise.", "id": "MarketplaceTargeting", "properties": {"geoTargeting": {"$ref": "CriteriaTargeting", "description": "Geo criteria IDs to be included/excluded."}, "inventorySizeTargeting": {"$ref": "InventorySizeTargeting", "description": "Inventory sizes to be included/excluded."}, "placementTargeting": {"$ref": "PlacementTargeting", "description": "Placement targeting information, for example, URL, mobile applications."}, "technologyTargeting": {"$ref": "TechnologyTargeting", "description": "Technology targeting information, for example, operating system, device category."}, "videoTargeting": {"$ref": "VideoTargeting", "description": "Video targeting information."}}, "type": "object"}, "MetricValue": {"description": "A metric value, with an expected value and a variance; represents a count that may be either exact or estimated (for example, when sampled).", "id": "MetricValue", "properties": {"value": {"description": "The expected value of the metric.", "format": "int64", "type": "string"}, "variance": {"description": "The variance (for example, square of the standard deviation) of the metric value. If value is exact, variance is 0. Can be used to calculate margin of error as a percentage of value, using the following formula, where Z is the standard constant that depends on the preferred size of the confidence interval (for example, for 90% confidence interval, use Z = 1.645): marginOfError = 100 * Z * sqrt(variance) / value", "format": "int64", "type": "string"}}, "type": "object"}, "MobileApplicationTargeting": {"description": "Mobile application targeting settings.", "id": "MobileApplicationTargeting", "properties": {"firstPartyTargeting": {"$ref": "FirstPartyMobileApplicationTargeting", "description": "Publisher owned apps to be targeted or excluded by the publisher to display the ads in."}}, "type": "object"}, "Money": {"description": "Represents an amount of money with its currency type.", "id": "Money", "properties": {"currencyCode": {"description": "The three-letter currency code defined in ISO 4217.", "type": "string"}, "nanos": {"description": "Number of nano (10^-9) units of the amount. The value must be between -999,999,999 and +999,999,999 inclusive. If `units` is positive, `nanos` must be positive or zero. If `units` is zero, `nanos` can be positive, zero, or negative. If `units` is negative, `nanos` must be negative or zero. For example $-1.75 is represented as `units`=-1 and `nanos`=-750,000,000.", "format": "int32", "type": "integer"}, "units": {"description": "The whole units of the amount. For example if `currencyCode` is `\"USD\"`, then 1 unit is one US dollar.", "format": "int64", "type": "string"}}, "type": "object"}, "NativeContent": {"description": "Native content for a creative.", "id": "NativeContent", "properties": {"advertiserName": {"description": "The name of the advertiser or sponsor, to be displayed in the ad creative.", "type": "string"}, "appIcon": {"$ref": "Image", "description": "The app icon, for app download ads."}, "body": {"description": "A long description of the ad.", "type": "string"}, "callToAction": {"description": "A label for the button that the user is supposed to click.", "type": "string"}, "clickLinkUrl": {"description": "The URL that the browser/SDK will load when the user clicks the ad.", "type": "string"}, "clickTrackingUrl": {"description": "The URL to use for click tracking.", "type": "string"}, "headline": {"description": "A short title for the ad.", "type": "string"}, "image": {"$ref": "Image", "description": "A large image."}, "logo": {"$ref": "Image", "description": "A smaller image, for the advertiser's logo."}, "priceDisplayText": {"description": "The price of the promoted app including currency info.", "type": "string"}, "starRating": {"description": "The app rating in the app store. Must be in the range [0-5].", "format": "double", "type": "number"}, "storeUrl": {"deprecated": true, "description": "The URL to the app store to purchase/download the promoted app.", "type": "string"}, "videoUrl": {"description": "The URL to fetch a native video ad.", "type": "string"}}, "type": "object"}, "NonBillableWinningBidStatusRow": {"description": "The number of winning bids with the specified dimension values for which the buyer was not billed, as described by the specified status.", "id": "NonBillableWinningBidStatusRow", "properties": {"bidCount": {"$ref": "MetricValue", "description": "The number of bids with the specified status."}, "rowDimensions": {"$ref": "RowDimensions", "description": "The values of all dimensions associated with metric values in this row."}, "status": {"description": "The status specifying why the winning bids were not billed.", "enum": ["STATUS_UNSPECIFIED", "AD_NOT_RENDERED", "INVALID_IMPRESSION", "FATAL_VAST_ERROR", "LOST_IN_MEDIATION"], "enumDescriptions": ["A placeholder for an undefined status. This value will never be returned in responses.", "The buyer was not billed because the ad was not rendered by the publisher.", "The buyer was not billed because the impression won by the bid was determined to be invalid.", "A video impression was served but a fatal error was reported from the client during playback.", "The buyer was not billed because the ad was outplaced in the mediation waterfall."], "type": "string"}}, "type": "object"}, "NonGuaranteedAuctionTerms": {"description": "Terms for Private Auctions. Note that Private Auctions can be created only by the seller, but they can be returned in a get or list request.", "id": "NonGuaranteedAuctionTerms", "properties": {"autoOptimizePrivateAuction": {"description": "True if open auction buyers are allowed to compete with invited buyers in this private auction.", "type": "boolean"}, "reservePricesPerBuyer": {"description": "Reserve price for the specified buyer.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "NonGuaranteedFixedPriceTerms": {"description": "Terms for Preferred Deals.", "id": "NonGuaranteedFixedPriceTerms", "properties": {"fixedPrices": {"description": "Fixed price for the specified buyer.", "items": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "Note": {"description": "A proposal may be associated to several notes.", "id": "Note", "properties": {"createTime": {"description": "Output only. The timestamp for when this note was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "creatorRole": {"description": "Output only. The role of the person (buyer/seller) creating the note.", "enum": ["BUYER_SELLER_ROLE_UNSPECIFIED", "BUYER", "SELLER"], "enumDescriptions": ["A placeholder for an undefined buyer/seller role.", "Specifies the role as buyer.", "Specifies the role as seller."], "readOnly": true, "type": "string"}, "note": {"description": "The actual note to attach. (max-length: 1024 unicode code units) Note: This field may be set only when creating the resource. Modifying this field while updating the resource will result in an error.", "type": "string"}, "noteId": {"description": "Output only. The unique ID for the note.", "readOnly": true, "type": "string"}, "proposalRevision": {"description": "Output only. The revision number of the proposal when the note is created.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "OperatingSystemTargeting": {"description": "Represents targeting information for operating systems.", "id": "OperatingSystemTargeting", "properties": {"operatingSystemCriteria": {"$ref": "CriteriaTargeting", "description": "IDs of operating systems to be included/excluded."}, "operatingSystemVersionCriteria": {"$ref": "CriteriaTargeting", "description": "IDs of operating system versions to be included/excluded."}}, "type": "object"}, "PauseProposalDealsRequest": {"description": "Request message to pause serving for finalized deals.", "id": "PauseProposalDealsRequest", "properties": {"externalDealIds": {"description": "The external_deal_id's of the deals to be paused. If empty, all the deals in the proposal will be paused.", "items": {"type": "string"}, "type": "array"}, "reason": {"description": "The reason why the deals are being paused. This human readable message will be displayed in the seller's UI. (Max length: 1000 unicode code units.)", "type": "string"}}, "type": "object"}, "PauseProposalRequest": {"description": "Request message to pause serving for an already-finalized proposal.", "id": "PauseProposalRequest", "properties": {"reason": {"description": "The reason why the proposal is being paused. This human readable message will be displayed in the seller's UI. (Max length: 1000 unicode code units.)", "type": "string"}}, "type": "object"}, "PlacementTargeting": {"description": "Represents targeting about where the ads can appear, for example, certain sites or mobile applications. Different placement targeting types will be logically OR'ed.", "id": "PlacementTargeting", "properties": {"mobileApplicationTargeting": {"$ref": "MobileApplicationTargeting", "description": "Mobile application targeting information in a deal. This doesn't apply to Auction Packages."}, "urlTargeting": {"$ref": "UrlTargeting", "description": "URLs to be included/excluded."}}, "type": "object"}, "PlatformContext": {"description": "Output only. The type of platform the restriction applies to.", "id": "PlatformContext", "properties": {"platforms": {"description": "The platforms this restriction applies to.", "items": {"enum": ["DESKTOP", "ANDROID", "IOS"], "enumDescriptions": ["Desktop platform.", "Android platform.", "iOS platform."], "type": "string"}, "type": "array"}}, "type": "object"}, "Price": {"description": "Represents a price and a pricing type for a product / deal.", "id": "Price", "properties": {"amount": {"$ref": "Money", "description": "The actual price with currency specified."}, "pricingType": {"description": "The pricing type for the deal/product. (default: CPM)", "enum": ["PRICING_TYPE_UNSPECIFIED", "COST_PER_MILLE", "COST_PER_DAY"], "enumDescriptions": ["A placeholder for an undefined pricing type. If the pricing type is unpsecified, `COST_PER_MILLE` will be used instead.", "Cost per thousand impressions.", "Cost per day"], "type": "string"}}, "type": "object"}, "PricePerBuyer": {"description": "Used to specify pricing rules for buyers/advertisers. Each PricePerBuyer in a product can become 0 or 1 deals. To check if there is a PricePerBuyer for a particular buyer or buyer/advertiser pair, we look for the most specific matching rule - we first look for a rule matching the buyer and advertiser, next a rule with the buyer but an empty advertiser list, and otherwise look for a matching rule where no buyer is set.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"advertiserIds": {"description": "The list of advertisers for this price when associated with this buyer. If empty, all advertisers with this buyer pay this price.", "items": {"type": "string"}, "type": "array"}, "buyer": {"$ref": "Buyer", "description": "The buyer who will pay this price. If unset, all buyers can pay this price (if the advertisers match, and there's no more specific rule matching the buyer)."}, "price": {"$ref": "Price", "description": "The specified price."}}, "type": "object"}, "PrivateData": {"description": "Buyers are allowed to store certain types of private data in a proposal/deal.", "id": "PrivateData", "properties": {"referenceId": {"description": "A buyer or seller specified reference ID. This can be queried in the list operations (max-length: 1024 unicode code units).", "type": "string"}}, "type": "object"}, "Product": {"description": "A product is a segment of inventory that a seller wants to sell. It is associated with certain terms and targeting information which helps the buyer know more about the inventory.", "id": "Product", "properties": {"availableEndTime": {"description": "The proposed end time for the deal. The field will be truncated to the order of seconds during serving.", "format": "google-datetime", "type": "string"}, "availableStartTime": {"description": "Inventory availability dates. The start time will be truncated to seconds during serving. Thus, a field specified as 3:23:34.456 (HH:mm:ss.SSS) will be truncated to 3:23:34 when serving.", "format": "google-datetime", "type": "string"}, "createTime": {"description": "Creation time.", "format": "google-datetime", "type": "string"}, "creatorContacts": {"description": "Optional contact information for the creator of this product.", "items": {"$ref": "ContactInformation"}, "type": "array"}, "displayName": {"description": "The display name for this product as set by the seller.", "type": "string"}, "hasCreatorSignedOff": {"description": "If the creator has already signed off on the product, then the buyer can finalize the deal by accepting the product as is. When copying to a proposal, if any of the terms are changed, then auto_finalize is automatically set to false.", "type": "boolean"}, "productId": {"description": "The unique ID for the product.", "type": "string"}, "productRevision": {"description": "The revision number of the product (auto-assigned by Marketplace).", "format": "int64", "type": "string"}, "publisherProfileId": {"description": "An ID which can be used by the Publisher Profile API to get more information about the seller that created this product.", "type": "string"}, "seller": {"$ref": "<PERSON><PERSON>", "description": "Information about the seller that created this product."}, "syndicationProduct": {"description": "The syndication product associated with the deal.", "enum": ["SYNDICATION_PRODUCT_UNSPECIFIED", "CONTENT", "MOBILE", "VIDEO", "GAMES"], "enumDescriptions": ["A placeholder for an undefined syndication product.", "This typically represents a web page.", "This represents a mobile property.", "This represents video ad formats.", "This represents ads shown within games."], "type": "string"}, "targetingCriterion": {"description": "Targeting that is shared between the buyer and the seller. Each targeting criterion has a specified key and for each key there is a list of inclusion value or exclusion values.", "items": {"$ref": "TargetingCriteria"}, "type": "array"}, "terms": {"$ref": "DealTerms", "description": "The negotiable terms of the deal."}, "updateTime": {"description": "Time of last update.", "format": "google-datetime", "type": "string"}, "webPropertyCode": {"description": "The web-property code for the seller. This needs to be copied as is when adding a new deal to a proposal.", "type": "string"}}, "type": "object"}, "Proposal": {"description": "Represents a proposal in the Marketplace. A proposal is the unit of negotiation between a seller and a buyer and contains deals which are served. Note: You can't update, create, or otherwise modify Private Auction deals through the API. Fields are updatable unless noted otherwise.", "id": "Proposal", "properties": {"billedBuyer": {"$ref": "Buyer", "description": "Output only. Reference to the buyer that will get billed for this proposal.", "readOnly": true}, "buyer": {"$ref": "Buyer", "description": "Reference to the buyer on the proposal. Note: This field may be set only when creating the resource. Modifying this field while updating the resource will result in an error."}, "buyerContacts": {"description": "Contact information for the buyer.", "items": {"$ref": "ContactInformation"}, "type": "array"}, "buyerPrivateData": {"$ref": "PrivateData", "description": "Private data for buyer. (hidden from seller)."}, "deals": {"description": "The deals associated with this proposal. For Private Auction proposals (whose deals have NonGuaranteedAuctionTerms), there will only be one deal.", "items": {"$ref": "Deal"}, "type": "array"}, "displayName": {"description": "The name for the proposal.", "type": "string"}, "isRenegotiating": {"description": "Output only. True if the proposal is being renegotiated.", "readOnly": true, "type": "boolean"}, "isSetupComplete": {"deprecated": true, "description": "Output only. True, if the buyside inventory setup is complete for this proposal.", "readOnly": true, "type": "boolean"}, "lastUpdaterOrCommentorRole": {"description": "Output only. The role of the last user that either updated the proposal or left a comment.", "enum": ["BUYER_SELLER_ROLE_UNSPECIFIED", "BUYER", "SELLER"], "enumDescriptions": ["A placeholder for an undefined buyer/seller role.", "Specifies the role as buyer.", "Specifies the role as seller."], "readOnly": true, "type": "string"}, "notes": {"description": "Output only. The notes associated with this proposal.", "items": {"$ref": "Note"}, "readOnly": true, "type": "array"}, "originatorRole": {"description": "Output only. Indicates whether the buyer/seller created the proposal.", "enum": ["BUYER_SELLER_ROLE_UNSPECIFIED", "BUYER", "SELLER"], "enumDescriptions": ["A placeholder for an undefined buyer/seller role.", "Specifies the role as buyer.", "Specifies the role as seller."], "readOnly": true, "type": "string"}, "privateAuctionId": {"description": "Output only. Private auction ID if this proposal is a private auction proposal.", "readOnly": true, "type": "string"}, "proposalId": {"description": "Output only. The unique ID of the proposal.", "readOnly": true, "type": "string"}, "proposalRevision": {"description": "Output only. The revision number for the proposal. Each update to the proposal or the deal causes the proposal revision number to auto-increment. The buyer keeps track of the last revision number they know of and pass it in when making an update. If the head revision number on the server has since incremented, then an ABORTED error is returned during the update operation to let the buyer know that a subsequent update was made.", "format": "int64", "readOnly": true, "type": "string"}, "proposalState": {"description": "Output only. The current state of the proposal.", "enum": ["PROPOSAL_STATE_UNSPECIFIED", "PROPOSED", "BUYER_ACCEPTED", "SELLER_ACCEPTED", "CANCELED", "FINALIZED"], "enumDescriptions": ["A placeholder for an undefined proposal state.", "The proposal is under negotiation or renegotiation.", "The proposal has been accepted by the buyer.", "The proposal has been accepted by the seller.", "The negotiations on the proposal were canceled and the proposal was never finalized.", "The proposal is finalized. During renegotiation, the proposal may not be in this state."], "readOnly": true, "type": "string"}, "seller": {"$ref": "<PERSON><PERSON>", "description": "Reference to the seller on the proposal. Note: This field may be set only when creating the resource. Modifying this field while updating the resource will result in an error."}, "sellerContacts": {"description": "Output only. Contact information for the seller.", "items": {"$ref": "ContactInformation"}, "readOnly": true, "type": "array"}, "termsAndConditions": {"description": "Output only. The terms and conditions set by the publisher for this proposal.", "readOnly": true, "type": "string"}, "updateTime": {"description": "Output only. The time when the proposal was last revised.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "PublisherProfile": {"description": "Represents a publisher profile (https://support.google.com/admanager/answer/6035806) in Marketplace. All fields are read only. All string fields are free-form text entered by the publisher unless noted otherwise.", "id": "PublisherProfile", "properties": {"audienceDescription": {"description": "Description on the publisher's audience.", "type": "string"}, "buyerPitchStatement": {"description": "Statement explaining what's unique about publisher's business, and why buyers should partner with the publisher.", "type": "string"}, "directDealsContact": {"description": "Contact information for direct reservation deals. This is free text entered by the publisher and may include information like names, phone numbers and email addresses.", "type": "string"}, "displayName": {"description": "Name of the publisher profile.", "type": "string"}, "domains": {"description": "The list of domains represented in this publisher profile. Empty if this is a parent profile. These are top private domains, meaning that these will not contain a string like \"photos.google.co.uk/123\", but will instead contain \"google.co.uk\".", "items": {"type": "string"}, "type": "array"}, "googlePlusUrl": {"description": "URL to publisher's Google+ page.", "type": "string"}, "isParent": {"description": "Indicates if this profile is the parent profile of the seller. A parent profile represents all the inventory from the seller, as opposed to child profile that is created to brand a portion of inventory. One seller should have only one parent publisher profile, and can have multiple child profiles. Publisher profiles for the same seller will have same value of field google.ads.adexchange.buyer.v2beta1.PublisherProfile.seller. See https://support.google.com/admanager/answer/6035806 for details.", "type": "boolean"}, "logoUrl": {"description": "A Google public URL to the logo for this publisher profile. The logo is stored as a PNG, JPG, or GIF image.", "type": "string"}, "mediaKitUrl": {"description": "URL to additional marketing and sales materials.", "type": "string"}, "mobileApps": {"description": "The list of apps represented in this publisher profile. Empty if this is a parent profile.", "items": {"$ref": "PublisherProfileMobileApplication"}, "type": "array"}, "overview": {"description": "Overview of the publisher.", "type": "string"}, "programmaticDealsContact": {"description": "Contact information for programmatic deals. This is free text entered by the publisher and may include information like names, phone numbers and email addresses.", "type": "string"}, "publisherProfileId": {"description": "Unique ID for publisher profile.", "type": "string"}, "rateCardInfoUrl": {"description": "URL to a publisher rate card.", "type": "string"}, "samplePageUrl": {"description": "URL to a sample content page.", "type": "string"}, "seller": {"$ref": "<PERSON><PERSON>", "description": "Seller of the publisher profile."}, "topHeadlines": {"description": "Up to three key metrics and rankings. Max 100 characters each. For example \"#1 Mobile News Site for 20 Straight Months\".", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "PublisherProfileMobileApplication": {"description": "A mobile application that contains a external app ID, name, and app store.", "id": "PublisherProfileMobileApplication", "properties": {"appStore": {"description": "The app store the app belongs to.", "enum": ["APP_STORE_TYPE_UNSPECIFIED", "APPLE_ITUNES", "GOOGLE_PLAY", "ROKU", "AMAZON_FIRETV", "PLAYSTATION", "XBOX", "SAMSUNG_TV", "AMAZON", "OPPO", "SAMSUNG", "VIVO", "XIAOMI", "LG_TV"], "enumDescriptions": ["A placeholder for an unknown app store.", "Apple iTunes", "Google Play", "Roku", "Amazon Fire TV", "Playstation", "Xbox", "Samsung TV", "Amazon Appstore", "OPPO App Market", "Samsung Galaxy Store", "VIVO App Store", "<PERSON><PERSON>", "LG TV"], "type": "string"}, "externalAppId": {"description": "The external ID for the app from its app store.", "type": "string"}, "name": {"description": "The name of the app.", "type": "string"}}, "type": "object"}, "RealtimeTimeRange": {"description": "An open-ended realtime time range specified by the start timestamp. For filter sets that specify a realtime time range RTB metrics continue to be aggregated throughout the lifetime of the filter set.", "id": "RealtimeTimeRange", "properties": {"startTimestamp": {"description": "The start timestamp of the real-time RTB metrics aggregation.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "RelativeDateRange": {"description": "A relative date range, specified by an offset and a duration. The supported range of dates begins 30 days before today and ends today, for example, the limits for these values are: offset_days >= 0 duration_days >= 1 offset_days + duration_days <= 30", "id": "RelativeDateRang<PERSON>", "properties": {"durationDays": {"description": "The number of days in the requested date range, for example, for a range spanning today: 1. For a range spanning the last 7 days: 7.", "format": "int32", "type": "integer"}, "offsetDays": {"description": "The end date of the filter set, specified as the number of days before today, for example, for a range where the last date is today: 0.", "format": "int32", "type": "integer"}}, "type": "object"}, "RemoveDealAssociationRequest": {"description": "A request for removing the association between a deal and a creative.", "id": "RemoveDealAssociationRequest", "properties": {"association": {"$ref": "CreativeDealAssociation", "description": "The association between a creative and a deal that should be removed."}}, "type": "object"}, "ResumeProposalDealsRequest": {"description": "Request message to resume (unpause) serving for already-finalized deals.", "id": "ResumeProposalDealsRequest", "properties": {"externalDealIds": {"description": "The external_deal_id's of the deals to resume. If empty, all the deals in the proposal will be resumed.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ResumeProposalRequest": {"description": "Request message to resume (unpause) serving for an already-finalized proposal.", "id": "ResumeProposalRequest", "properties": {}, "type": "object"}, "RowDimensions": {"description": "A response may include multiple rows, breaking down along various dimensions. Encapsulates the values of all dimensions for a given row.", "id": "RowDimensions", "properties": {"publisherIdentifier": {"description": "The publisher identifier for this row, if a breakdown by [BreakdownDimension.PUBLISHER_IDENTIFIER](https://developers.google.com/authorized-buyers/apis/reference/rest/v2beta1/bidders.accounts.filterSets#FilterSet.BreakdownDimension) was requested.", "type": "string"}, "timeInterval": {"$ref": "TimeInterval", "description": "The time interval that this row represents."}}, "type": "object"}, "SecurityContext": {"deprecated": true, "description": "Output only. A security context.", "id": "SecurityContext", "properties": {"securities": {"description": "The security types in this context.", "items": {"enum": ["INSECURE", "SSL"], "enumDescriptions": ["Matches impressions that require insecure compatibility.", "Matches impressions that require SSL compatibility."], "type": "string"}, "type": "array"}}, "type": "object"}, "Seller": {"description": "Represents a seller of inventory. Each seller is identified by a unique Ad Manager account ID.", "id": "<PERSON><PERSON>", "properties": {"accountId": {"description": "The unique ID for the seller. The seller fills in this field. The seller account ID is then available to buyer in the product.", "type": "string"}, "subAccountId": {"description": "Output only. Ad manager network code for the seller.", "readOnly": true, "type": "string"}}, "type": "object"}, "ServingContext": {"description": "The serving context for this restriction.", "id": "ServingContext", "properties": {"all": {"description": "Matches all contexts.", "enum": ["SIMPLE_CONTEXT"], "enumDescriptions": ["A simple context."], "type": "string"}, "appType": {"$ref": "AppContext", "description": "Matches impressions for a particular app type."}, "auctionType": {"$ref": "AuctionContext", "description": "Matches impressions for a particular auction type."}, "location": {"$ref": "LocationContext", "description": "Matches impressions coming from users *or* publishers in a specific location."}, "platform": {"$ref": "PlatformContext", "description": "Matches impressions coming from a particular platform."}, "securityType": {"$ref": "SecurityContext", "deprecated": true, "description": "Matches impressions for a particular security type."}}, "type": "object"}, "ServingRestriction": {"description": "Output only. A representation of the status of an ad in a specific context. A context here relates to where something ultimately serves (for example, a user or publisher geo, a platform, an HTTPS versus HTTP request, or the type of auction).", "id": "ServingRestriction", "properties": {"contexts": {"description": "The contexts for the restriction.", "items": {"$ref": "ServingContext"}, "type": "array"}, "disapproval": {"$ref": "Disapproval", "description": "Disapproval bound to this restriction. Only present if status=DISAPPROVED. Can be used to filter the response of the creatives.list method."}, "disapprovalReasons": {"deprecated": true, "description": "Any disapprovals bound to this restriction. Only present if status=DISAPPROVED. Can be used to filter the response of the creatives.list method. Deprecated; use disapproval field instead.", "items": {"$ref": "Disapproval"}, "type": "array"}, "status": {"description": "The status of the creative in this context (for example, it has been explicitly disapproved or is pending review).", "enum": ["STATUS_UNSPECIFIED", "DISAPPROVAL", "PENDING_REVIEW"], "enumDescriptions": ["The status is not known.", "The ad was disapproved in this context.", "The ad is pending review in this context."], "type": "string"}}, "type": "object"}, "Size": {"description": "Message depicting the size of the creative. The units of width and height depend on the type of the targeting.", "id": "Size", "properties": {"height": {"description": "The height of the creative.", "format": "int32", "type": "integer"}, "width": {"description": "The width of the creative", "format": "int32", "type": "integer"}}, "type": "object"}, "StopWatchingCreativeRequest": {"description": "A request for stopping notifications for changes to creative Status.", "id": "StopWatchingCreativeRequest", "properties": {}, "type": "object"}, "TargetingCriteria": {"description": "Advertisers can target different attributes of an ad slot. For example, they can choose to show ads only if the user is in the U.S. Such targeting criteria can be specified as part of Shared Targeting.", "id": "TargetingCriteria", "properties": {"exclusions": {"description": "The list of values to exclude from targeting. Each value is AND'd together.", "items": {"$ref": "TargetingValue"}, "type": "array"}, "inclusions": {"description": "The list of value to include as part of the targeting. Each value is OR'd together.", "items": {"$ref": "TargetingValue"}, "type": "array"}, "key": {"description": "The key representing the shared targeting criterion. Targeting criteria defined by Google ad servers will begin with GOOG_. Third parties may define their own keys. A list of permissible keys along with the acceptable values will be provided as part of the external documentation.", "type": "string"}}, "type": "object"}, "TargetingValue": {"description": "A polymorphic targeting value used as part of Shared Targeting.", "id": "TargetingValue", "properties": {"creativeSizeValue": {"$ref": "CreativeSize", "description": "The creative size value to include/exclude. Filled in when key = GOOG_CREATIVE_SIZE"}, "dayPartTargetingValue": {"$ref": "DayPartTargeting", "description": "The daypart targeting to include / exclude. Filled in when the key is GOOG_DAYPART_TARGETING. The definition of this targeting is derived from the structure used by Ad Manager."}, "longValue": {"description": "The long value to include/exclude.", "format": "int64", "type": "string"}, "stringValue": {"description": "The string value to include/exclude.", "type": "string"}}, "type": "object"}, "TechnologyTargeting": {"description": "Represents targeting about various types of technology.", "id": "TechnologyTargeting", "properties": {"deviceCapabilityTargeting": {"$ref": "CriteriaTargeting", "description": "IDs of device capabilities to be included/excluded."}, "deviceCategoryTargeting": {"$ref": "CriteriaTargeting", "description": "IDs of device categories to be included/excluded."}, "operatingSystemTargeting": {"$ref": "OperatingSystemTargeting", "description": "Operating system related targeting information."}}, "type": "object"}, "TimeInterval": {"description": "An interval of time, with an absolute start and end.", "id": "TimeInterval", "properties": {"endTime": {"description": "The timestamp marking the end of the range (exclusive) for which data is included.", "format": "google-datetime", "type": "string"}, "startTime": {"description": "The timestamp marking the start of the range (inclusive) for which data is included.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "TimeOfDay": {"description": "Represents a time of day. The date and time zone are either not significant or are specified elsewhere. An API may choose to allow leap seconds. Related types are google.type.Date and `google.protobuf.Timestamp`.", "id": "TimeOfDay", "properties": {"hours": {"description": "Hours of a day in 24 hour format. Must be greater than or equal to 0 and typically must be less than or equal to 23. An API may choose to allow the value \"24:00:00\" for scenarios like business closing time.", "format": "int32", "type": "integer"}, "minutes": {"description": "Minutes of an hour. Must be greater than or equal to 0 and less than or equal to 59.", "format": "int32", "type": "integer"}, "nanos": {"description": "Fractions of seconds, in nanoseconds. Must be greater than or equal to 0 and less than or equal to 999,999,999.", "format": "int32", "type": "integer"}, "seconds": {"description": "Seconds of a minute. Must be greater than or equal to 0 and typically must be less than or equal to 59. An API may allow the value 60 if it allows leap-seconds.", "format": "int32", "type": "integer"}}, "type": "object"}, "UrlTargeting": {"description": "Represents a list of targeted and excluded URLs (for example, google.com). For Private Auction and AdX Preferred Deals, URLs are either included or excluded. For Programmatic Guaranteed and Preferred Deals, this doesn't apply.", "id": "UrlTargeting", "properties": {"excludedUrls": {"description": "A list of URLs to be excluded.", "items": {"type": "string"}, "type": "array"}, "targetedUrls": {"description": "A list of URLs to be included.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "VideoContent": {"description": "Video content for a creative.", "id": "VideoContent", "properties": {"videoUrl": {"description": "The URL to fetch a video ad.", "type": "string"}, "videoVastXml": {"description": "The contents of a VAST document for a video ad. This document should conform to the VAST 2.0 or 3.0 standard.", "type": "string"}}, "type": "object"}, "VideoTargeting": {"description": "Represents targeting information about video.", "id": "VideoTargeting", "properties": {"excludedPositionTypes": {"description": "A list of video positions to be excluded. Position types can either be included or excluded (XOR).", "items": {"enum": ["POSITION_TYPE_UNSPECIFIED", "PREROLL", "MIDROLL", "POSTROLL"], "enumDescriptions": ["A placeholder for an undefined video position.", "Ad is played before the video.", "Ad is played during the video.", "Ad is played after the video."], "type": "string"}, "type": "array"}, "targetedPositionTypes": {"description": "A list of video positions to be included. When the included list is present, the excluded list must be empty. When the excluded list is present, the included list must be empty.", "items": {"enum": ["POSITION_TYPE_UNSPECIFIED", "PREROLL", "MIDROLL", "POSTROLL"], "enumDescriptions": ["A placeholder for an undefined video position.", "Ad is played before the video.", "Ad is played during the video.", "Ad is played after the video."], "type": "string"}, "type": "array"}}, "type": "object"}, "WatchCreativeRequest": {"description": "A request for watching changes to creative Status.", "id": "WatchCreativeRequest", "properties": {"topic": {"description": "The Pub/Sub topic to publish notifications to. This topic must already exist and must give <NAME_EMAIL> to write to the topic. This should be the full resource name in \"projects/{project_id}/topics/{topic_id}\" format.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Ad Exchange Buyer API II", "version": "v2beta1", "version_module": true}
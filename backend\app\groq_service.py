import os
from groq import <PERSON><PERSON><PERSON>
from typing import List
from models import ChatMessage
from buddy_personality import get_buddy_system_prompt, is_general_knowledge_question, GENERAL_KNOWLEDGE_DEFLECTIONS
import random

class GroqService:
    def __init__(self):
        api_key = os.getenv("GROQ_API_KEY")
        if not api_key:
            raise ValueError("GROQ_API_KEY environment variable is required")
        
        self.client = Groq(api_key=api_key)
        self.model = "llama3-8b-8192"  # Using Llama 3 model
    
    async def get_buddy_response(self, user_message: str, conversation_history: List[ChatMessage] = None) -> str:
        """
        Get a response from the buddy using Groq API
        """
        try:
            # Check if it's a general knowledge question
            if is_general_knowledge_question(user_message):
                # Return a random deflection response
                return random.choice(GENERAL_KNOWLEDGE_DEFLECTIONS)
            
            # Prepare conversation history
            messages = [
                {"role": "system", "content": get_buddy_system_prompt()}
            ]
            
            # Add conversation history if provided
            if conversation_history:
                for msg in conversation_history[-10:]:  # Keep last 10 messages for context
                    messages.append({
                        "role": msg.role,
                        "content": msg.content
                    })
            
            # Add current user message
            messages.append({
                "role": "user",
                "content": user_message
            })
            
            # Get response from Groq
            response = self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                max_tokens=500,
                temperature=0.8,  # Make responses more creative and varied
                top_p=0.9
            )
            
            buddy_response = response.choices[0].message.content.strip()
            
            # Ensure the response maintains buddy personality
            if not any(slang in buddy_response.lower() for slang in ["dude", "bro", "yo", "man", "buddy"]):
                # Add some slang if the response is too formal
                buddy_response = f"Yo! {buddy_response}"
            
            return buddy_response
            
        except Exception as e:
            print(f"Error getting Groq response: {e}")
            return "Yo dude, something went wrong on my end! 😅 Can you try asking me again? I'm usually better than this, I promise!"

# Create a singleton instance
groq_service = GroqService()
